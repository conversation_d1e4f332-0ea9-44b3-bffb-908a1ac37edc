<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>西安游戏开发|电商系统搭建|APP定制-未来基石科技（10年经验）</title>
    <meta name="description" content="西北地区专业数字化服务商，提供游戏/电商/企业官网/APP开发，10年技术团队200+案例验证">
    <meta name="keywords" content="陕西软件开发,手游开发公司,跨境电商解决方案,西安APP开发,企业官网建设">
    <!-- 引入 Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        // 苹果风格配置
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'apple-blue': '#007AFF',
                        'apple-gray': {
                            50: '#F2F2F7',
                            100: '#E5E5EA',
                            200: '#D1D1D6',
                            300: '#C7C7CC',
                            400: '#AEAEB2',
                            500: '#8E8E93',
                            600: '#636366',
                            700: '#48484A',
                            800: '#3A3A3C',
                            900: '#1C1C1E'
                        },
                        'tech-blue': {
                            light: '#60a5fa',
                            DEFAULT: '#3b82f6',
                            dark: '#2563eb',
                        },
                        'tech-purple': {
                            light: '#a78bfa',
                            DEFAULT: '#8b5cf6',
                            dark: '#7c3aed',
                        },
                        dark: {
                            100: '#282c34',
                            200: '#21252b',
                            300: '#1c1e24',
                            400: '#181a1f',
                        }
                    },
                    fontFamily: {
                        'sf': ['-apple-system', 'BlinkMacSystemFont', 'SF Pro Display', 'Segoe UI', 'Roboto', 'sans-serif']
                    },
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                        'slide-up': 'slideUp 0.8s ease-out forwards',
                        'slide-down': 'slideDown 0.8s ease-out forwards',
                        'slide-left': 'slideLeft 0.8s ease-out forwards',
                        'slide-right': 'slideRight 0.8s ease-out forwards',
                        'fade-in': 'fadeIn 0.8s ease-out forwards',
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'code-flow': 'codeFlow 20s linear infinite',
                    },
                    keyframes: {
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-20px)' },
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(50px)', opacity: 0 },
                            '100%': { transform: 'translateY(0)', opacity: 1 },
                        },
                        slideDown: {
                            '0%': { transform: 'translateY(-50px)', opacity: 0 },
                            '100%': { transform: 'translateY(0)', opacity: 1 },
                        },
                        slideLeft: {
                            '0%': { transform: 'translateX(50px)', opacity: 0 },
                            '100%': { transform: 'translateX(0)', opacity: 1 },
                        },
                        slideRight: {
                            '0%': { transform: 'translateX(-50px)', opacity: 0 },
                            '100%': { transform: 'translateX(0)', opacity: 1 },
                        },
                        fadeIn: {
                            '0%': { opacity: 0 },
                            '100%': { opacity: 1 },
                        },
                        codeFlow: {
                            '0%': { transform: 'translateY(0)' },
                            '100%': { transform: 'translateY(-50%)' },
                        }
                    }
                }
            }
        }
    </script>
    <style>
        /* 苹果风格滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #21252b;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #3b82f6;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #2563eb;
        }

        html,
        body {
            scroll-behavior: smooth;
            background-color: #0f172a;
            color: #f9fafb;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Roboto, sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        section {
            padding: 100px 0;
            position: relative;
            overflow: hidden;
        }

        /* 渐变文字 */
        .text-gradient {
            background-clip: text;
            -webkit-background-clip: text;
            color: transparent;
        }

        .text-gradient-blue {
            background-image: linear-gradient(90deg, #3b82f6, #8b5cf6);
        }

        /* 浮动背景形状 */
        .floating-shape {
            position: absolute;
            border-radius: 50%;
            filter: blur(40px);
            z-index: 0;
            opacity: 0.3;
        }

        /* 观察者动画 */
        .animate-on-scroll {
            opacity: 0;
        }

        .animate-on-scroll.animated {
            animation-play-state: running;
        }

        /* 苹果风格悬浮卡片效果 - 去除阴影，减少3D效果 */
        .hover-card {
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .hover-card:hover {
            transform: translateY(-4px);
            border-color: rgba(59, 130, 246, 0.3);
        }

        /* 苹果风格毛玻璃效果 */
        .glass {
            background: rgba(15, 23, 42, 0.8);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* 苹果风格按钮 */
        .btn-gradient {
            background-size: 200% auto;
            transition: all 0.3s ease;
            border-radius: 8px;
        }

        .btn-gradient:hover {
            background-position: right center;
            transform: translateY(-1px);
        }

        .btn-gradient-blue {
            background-image: linear-gradient(45deg, #3b82f6 0%, #8b5cf6 50%, #3b82f6 100%);
        }

        /* 苹果风格闪光效果 */
        .shimmer {
            position: relative;
            overflow: hidden;
        }

        .shimmer::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(to bottom right,
                    rgba(255, 255, 255, 0) 0%,
                    rgba(255, 255, 255, 0.08) 50%,
                    rgba(255, 255, 255, 0) 100%);
            transform: rotate(30deg);
            animation: shimmer 4s infinite;
        }

        @keyframes shimmer {
            0% {
                transform: translateX(-100%) rotate(30deg);
            }

            100% {
                transform: translateX(100%) rotate(30deg);
            }
        }

        /* 代码流效果 */
        .code-background {
            position: relative;
            overflow: hidden;
        }

        .code-flow {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 200%;
            opacity: 0.15;
            background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwMCI+PHRleHQgeD0iMCIgeT0iMjAiIGZpbGw9IiMzYjgyZjYiIGZvbnQtZmFtaWx5PSJtb25vc3BhY2UiIGZvbnQtc2l6ZT0iMTJweCI+e2Z1bmN0aW9uKCl7cmV0dXJuIGRhdGF9fTwvdGV4dD48dGV4dCB4PSIwIiB5PSI0MCIgZmlsbD0iIzNiODJmNiIgZm9udC1mYW1pbHk9Im1vbm9zcGFjZSIgZm9udC1zaXplPSIxMnB4Ij5jb25zdCBkYXRhID0gZmV0Y2goKTs8L3RleHQ+PHRleHQgeD0iMCIgeT0iNjAiIGZpbGw9IiMzYjgyZjYiIGZvbnQtZmFtaWx5PSJtb25vc3BhY2UiIGZvbnQtc2l6ZT0iMTJweCI+ZnVuY3Rpb24gcmVuZGVyKCkgezwvdGV4dD48dGV4dCB4PSIwIiB5PSI4MCIgZmlsbD0iIzNiODJmNiIgZm9udC1mYW1pbHk9Im1vbm9zcGFjZSIgZm9udC1zaXplPSIxMnB4Ij4gIHJldHVybiA8ZGl2PjwvZGl2Pjs8L3RleHQ+PHRleHQgeD0iMCIgeT0iMTAwIiBmaWxsPSIjM2I4MmY2IiBmb250LWZhbWlseT0ibW9ub3NwYWNlIiBmb250LXNpemU9IjEycHgiPn08L3RleHQ+PHRleHQgeD0iMCIgeT0iMTIwIiBmaWxsPSIjOGI1Y2Y2IiBmb250LWZhbWlseT0ibW9ub3NwYWNlIiBmb250LXNpemU9IjEycHgiPmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFwcCgpIHs8L3RleHQ+PHRleHQgeD0iMCIgeT0iMTQwIiBmaWxsPSIjOGI1Y2Y2IiBmb250LWZhbWlseT0ibW9ub3NwYWNlIiBmb250LXNpemU9IjEycHgiPiAgcmV0dXJuICgKICAgIDxkaXY+PC9kaXY+Cik7PC90ZXh0Pjx0ZXh0IHg9IjAiIHk9IjE2MCIgZmlsbD0iIzhiNWNmNiIgZm9udC1mYW1pbHk9Im1vbm9zcGFjZSIgZm9udC1zaXplPSIxMnB4Ij59PC90ZXh0Pjx0ZXh0IHg9IjAiIHk9IjE4MCIgZmlsbD0iIzNiODJmNiIgZm9udC1mYW1pbHk9Im1vbm9zcGFjZSIgZm9udC1zaXplPSIxMnB4Ij4=PC90ZXh0PjwvdGV4dD48L3N2Zz4=');
            background-repeat: repeat-y;
            animation: code-flow 20s linear infinite;
        }

        /* 时间轴样式 */
        .timeline {
            position: relative;
            padding-left: 2rem;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 2px;
            height: 100%;
            background: linear-gradient(to bottom, #3b82f6, #8b5cf6);
        }

        .timeline-item {
            position: relative;
            padding-bottom: 2.5rem;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -2rem;
            top: 0;
            width: 1rem;
            height: 1rem;
            border-radius: 50%;
            background-color: #3b82f6;
            border: 2px solid #0f172a;
        }

        /* 苹果风格圆角 - 适中大小 */
        .rounded-apple {
            border-radius: 8px;
        }

        .rounded-apple-lg {
            border-radius: 12px;
        }

        .rounded-apple-xl {
            border-radius: 16px;
        }
    </style>
</head>

<body class="bg-dark-300 text-gray-100 font-sf">
    <!-- 固定导航栏 -->
    <header id="header" class="fixed w-full glass z-50 transition-all duration-500">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <a href="#" class="flex items-center gap-2 group">
                    <img src="./images/logo.svg" alt="未来基石logo"
                        class="h-10 transition-all duration-500 group-hover:rotate-12">
                    <span class="text-xl font-bold text-gradient text-gradient-blue"
                        style="margin-left: 20px;line-height: 1;">
                        <div>Future Foundation</div>
                        <div>未来基石科技</div>
                    </span>
                </a>
                <nav class="hidden md:block">
                    <ul class="flex items-center gap-8">
                        <li><a href="#hero"
                                class="relative text-tech-blue font-medium pb-1 after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-full after:h-0.5 after:bg-gradient-to-r after:from-tech-blue after:to-tech-purple">首页</a>
                        </li>
                        <li><a href="#about"
                                class="relative text-gray-300 font-medium pb-1 hover:text-tech-blue after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-gradient-to-r after:from-tech-blue after:to-tech-purple hover:after:w-full after:transition-all after:duration-300">公司简介</a>
                        </li>
                        <li><a href="#services"
                                class="relative text-gray-300 font-medium pb-1 hover:text-tech-blue after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-gradient-to-r after:from-tech-blue after:to-tech-purple hover:after:w-full after:transition-all after:duration-300">业务板块</a>
                        </li>
                        <li><a href="#technology"
                                class="relative text-gray-300 font-medium pb-1 hover:text-tech-blue after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-gradient-to-r after:from-tech-blue after:to-tech-purple hover:after:w-full after:transition-all after:duration-300">技术优势</a>
                        </li>
                        <li><a href="#portfolio"
                                class="relative text-gray-300 font-medium pb-1 hover:text-tech-blue after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-gradient-to-r after:from-tech-blue after:to-tech-purple hover:after:w-full after:transition-all after:duration-300">案例展示</a>
                        </li>
                        <li><a href="#contact"
                                class="relative text-gray-300 font-medium pb-1 hover:text-tech-blue after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-gradient-to-r after:from-tech-blue after:to-tech-purple hover:after:w-full after:transition-all after:duration-300">联系我们</a>
                        </li>
                    </ul>
                </nav>
                <button id="mobile-toggle" class="block md:hidden text-2xl text-tech-blue">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
        </div>
        <!-- 移动端菜单 -->
        <div id="mobile-menu"
            class="fixed inset-0 glass z-40 transform translate-x-full transition-transform duration-500 md:hidden">
            <div class="container mx-auto px-4 py-5">
                <div class="flex justify-between items-center mb-8">
                    <a href="#" class="flex items-center gap-2">
                        <img src="./images/logo.svg" alt="未来基石logo" class="h-10">
                        <span class="text-xl font-bold text-gradient text-gradient-blue">陕西未来基石科技</span>
                    </a>
                    <button id="mobile-close"
                        class="text-2xl text-tech-blue transition-transform duration-300 hover:rotate-90">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <ul class="flex flex-col gap-6">
                    <li><a href="#hero"
                            class="block text-lg font-medium text-tech-blue py-2 border-b border-gray-700">首页</a></li>
                    <li><a href="#about"
                            class="block text-lg font-medium text-gray-300 hover:text-tech-blue py-2 border-b border-gray-700">公司简介</a>
                    </li>
                    <li><a href="#services"
                            class="block text-lg font-medium text-gray-300 hover:text-tech-blue py-2 border-b border-gray-700">业务板块</a>
                    </li>
                    <li><a href="#technology"
                            class="block text-lg font-medium text-gray-300 hover:text-tech-blue py-2 border-b border-gray-700">技术优势</a>
                    </li>
                    <li><a href="#portfolio"
                            class="block text-lg font-medium text-gray-300 hover:text-tech-blue py-2 border-b border-gray-700">案例展示</a>
                    </li>
                    <li><a href="#news"
                            class="block text-lg font-medium text-gray-300 hover:text-tech-blue py-2 border-b border-gray-700">新闻动态</a>
                    </li>
                    <li><a href="#contact"
                            class="block text-lg font-medium text-gray-300 hover:text-tech-blue py-2 border-b border-gray-700">联系我们</a>
                    </li>
                </ul>
            </div>
        </div>
    </header>
