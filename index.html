<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>西安游戏开发|电商系统搭建|APP定制-未来基石科技（10年经验）</title>
    <meta name="description" content="西北地区专业数字化服务商，提供游戏/电商/企业官网/APP开发，10年技术团队200+案例验证">
    <meta name="keywords" content="陕西软件开发,手游开发公司,跨境电商解决方案,西安APP开发,企业官网建设">
    <!-- 引入 Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        // 自定义 Tailwind 配置
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'vue-green': {
                            light: '#4ade80',
                            DEFAULT: '#42b883',
                            dark: '#3a9d7e',
                        },
                        'vue-blue': {
                            light: '#38bdf8',
                            DEFAULT: '#35495e',
                            dark: '#1e293b',
                        },
                        'tech-blue': {
                            light: '#60a5fa',
                            DEFAULT: '#3b82f6',
                            dark: '#2563eb',
                        },
                        'tech-purple': {
                            light: '#a78bfa',
                            DEFAULT: '#8b5cf6',
                            dark: '#7c3aed',
                        },
                        dark: {
                            100: '#282c34',
                            200: '#21252b',
                            300: '#1c1e24',
                            400: '#181a1f',
                        }
                    },
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                        'slide-up': 'slideUp 0.8s ease-out forwards',
                        'slide-down': 'slideDown 0.8s ease-out forwards',
                        'slide-left': 'slideLeft 0.8s ease-out forwards',
                        'slide-right': 'slideRight 0.8s ease-out forwards',
                        'fade-in': 'fadeIn 0.8s ease-out forwards',
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'code-flow': 'codeFlow 20s linear infinite',
                    },
                    keyframes: {
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-20px)' },
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(50px)', opacity: 0 },
                            '100%': { transform: 'translateY(0)', opacity: 1 },
                        },
                        slideDown: {
                            '0%': { transform: 'translateY(-50px)', opacity: 0 },
                            '100%': { transform: 'translateY(0)', opacity: 1 },
                        },
                        slideLeft: {
                            '0%': { transform: 'translateX(50px)', opacity: 0 },
                            '100%': { transform: 'translateX(0)', opacity: 1 },
                        },
                        slideRight: {
                            '0%': { transform: 'translateX(-50px)', opacity: 0 },
                            '100%': { transform: 'translateX(0)', opacity: 1 },
                        },
                        fadeIn: {
                            '0%': { opacity: 0 },
                            '100%': { opacity: 1 },
                        },
                        codeFlow: {
                            '0%': { transform: 'translateY(0)' },
                            '100%': { transform: 'translateY(-50%)' },
                        }
                    }
                }
            }
        }
    </script>
    <!-- 字体图标 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #21252b;
        }

        ::-webkit-scrollbar-thumb {
            background: #3b82f6;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #2563eb;
        }

        html,
        body {
            scroll-behavior: smooth;
            background-color: #0f172a;
            color: #f9fafb;
        }

        section {
            padding: 100px 0;
            position: relative;
            overflow: hidden;
        }

        /* 渐变文字 */
        .text-gradient {
            background-clip: text;
            -webkit-background-clip: text;
            color: transparent;
        }

        .text-gradient-blue {
            background-image: linear-gradient(90deg, #3b82f6, #8b5cf6);
        }

        /* 浮动背景形状 */
        .floating-shape {
            position: absolute;
            border-radius: 50%;
            filter: blur(40px);
            z-index: 0;
            opacity: 0.3;
        }

        /* 观察者动画 */
        .animate-on-scroll {
            opacity: 0;
        }

        .animate-on-scroll.animated {
            animation-play-state: running;
        }

        /* 悬浮卡片效果 */
        .hover-card {
            transition: all 0.5s ease;
            transform-style: preserve-3d;
            perspective: 1000px;
        }

        .hover-card:hover {
            transform: translateY(-10px) rotateX(5deg);
        }

        /* 毛玻璃效果 */
        .glass {
            background: rgba(15, 23, 42, 0.6);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.05);
        }

        /* 渐变按钮 */
        .btn-gradient {
            background-size: 200% auto;
            transition: all 0.5s ease;
        }

        .btn-gradient:hover {
            background-position: right center;
        }

        .btn-gradient-blue {
            background-image: linear-gradient(45deg, #3b82f6 0%, #8b5cf6 50%, #3b82f6 100%);
        }

        /* 闪光效果 */
        .shimmer {
            position: relative;
            overflow: hidden;
        }

        .shimmer::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(to bottom right,
                    rgba(255, 255, 255, 0) 0%,
                    rgba(255, 255, 255, 0.1) 50%,
                    rgba(255, 255, 255, 0) 100%);
            transform: rotate(30deg);
            animation: shimmer 4s infinite;
        }

        @keyframes shimmer {
            0% {
                transform: translateX(-100%) rotate(30deg);
            }

            100% {
                transform: translateX(100%) rotate(30deg);
            }
        }

        /* 代码流效果 */
        .code-background {
            position: relative;
            overflow: hidden;
        }

        .code-flow {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 200%;
            opacity: 0.15;
            background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwMCI+PHRleHQgeD0iMCIgeT0iMjAiIGZpbGw9IiMzYjgyZjYiIGZvbnQtZmFtaWx5PSJtb25vc3BhY2UiIGZvbnQtc2l6ZT0iMTJweCI+e2Z1bmN0aW9uKCl7cmV0dXJuIGRhdGF9fTwvdGV4dD48dGV4dCB4PSIwIiB5PSI0MCIgZmlsbD0iIzNiODJmNiIgZm9udC1mYW1pbHk9Im1vbm9zcGFjZSIgZm9udC1zaXplPSIxMnB4Ij5jb25zdCBkYXRhID0gZmV0Y2goKTs8L3RleHQ+PHRleHQgeD0iMCIgeT0iNjAiIGZpbGw9IiMzYjgyZjYiIGZvbnQtZmFtaWx5PSJtb25vc3BhY2UiIGZvbnQtc2l6ZT0iMTJweCI+ZnVuY3Rpb24gcmVuZGVyKCkgezwvdGV4dD48dGV4dCB4PSIwIiB5PSI4MCIgZmlsbD0iIzNiODJmNiIgZm9udC1mYW1pbHk9Im1vbm9zcGFjZSIgZm9udC1zaXplPSIxMnB4Ij4gIHJldHVybiA8ZGl2PjwvZGl2Pjs8L3RleHQ+PHRleHQgeD0iMCIgeT0iMTAwIiBmaWxsPSIjM2I4MmY2IiBmb250LWZhbWlseT0ibW9ub3NwYWNlIiBmb250LVNpemU9IjEycHgiPn08L3RleHQ+PHRleHQgeD0iMCIgeT0iMTIwIiBmaWxsPSIjOGI1Y2Y2IiBmb250LWZhbWlseT0ibW9ub3NwYWNlIiBmb250LXNpemU9IjEycHgiPmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFwcCgpIHs8L3RleHQ+PHRleHQgeD0iMCIgeT0iMTQwIiBmaWxsPSIjOGI1Y2Y2IiBmb250LWZhbWlseT0ibW9ub3NwYWNlIiBmb250LXNpemU9IjEycHgiPiAgcmV0dXJuICgKICAgIDxkaXY+PC9kaXY+Cik7PC90ZXh0Pjx0ZXh0IHg9IjAiIHk9IjE2MCIgZmlsbD0iIzhiNWNmNiIgZm9udC1mYW1pbHk9Im1vbm9zcGFjZSIgZm9udC1zaXplPSIxMnB4Ij59PC90ZXh0Pjx0ZXh0IHg9IjAiIHk9IjE4MCIgZmlsbD0iIzNiODJmNiIgZm9udC1mYW1pbHk9Im1vbm9zcGFjZSIgZm9udC1zaXplPSIxMnB4Ij4=PC90ZXh0PjwvdGV4dD48L3N2Zz4=');
            background-repeat: repeat-y;
            animation: code-flow 20s linear infinite;
        }

        /* 时间轴样式 */
        .timeline {
            position: relative;
            padding-left: 2rem;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 2px;
            height: 100%;
            background: linear-gradient(to bottom, #3b82f6, #8b5cf6);
        }

        .timeline-item {
            position: relative;
            padding-bottom: 2.5rem;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -2rem;
            top: 0;
            width: 1rem;
            height: 1rem;
            border-radius: 50%;
            background-color: #3b82f6;
            border: 2px solid #0f172a;
            box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
        }
    </style>
</head>

<body class="bg-dark-300 text-gray-100 font-sans">
    <!-- 固定导航栏 -->
    <header id="header" class="fixed w-full glass z-50 transition-all duration-500">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <a href="#" class="flex items-center gap-2 group">
                    <img src="./images/logo.svg" alt="未来基石logo"
                        class="h-10 transition-all duration-500 group-hover:rotate-12">
                    <span class="text-xl font-bold text-gradient text-gradient-blue"
                        style="margin-left: 20px;line-height: 1;">
                        <div>Future Foundation</div>
                        <div>未来基石科技</div>
                    </span>
                </a>
                <nav class="hidden md:block">
                    <ul class="flex items-center gap-8">
                        <li><a href="#hero"
                                class="relative text-tech-blue font-medium pb-1 after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-full after:h-0.5 after:bg-gradient-to-r after:from-tech-blue after:to-tech-purple">首页</a>
                        </li>
                        <li><a href="#about"
                                class="relative text-gray-300 font-medium pb-1 hover:text-tech-blue after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-gradient-to-r after:from-tech-blue after:to-tech-purple hover:after:w-full after:transition-all after:duration-300">公司简介</a>
                        </li>
                        <li><a href="#services"
                                class="relative text-gray-300 font-medium pb-1 hover:text-tech-blue after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-gradient-to-r after:from-tech-blue after:to-tech-purple hover:after:w-full after:transition-all after:duration-300">业务板块</a>
                        </li>
                        <li><a href="#technology"
                                class="relative text-gray-300 font-medium pb-1 hover:text-tech-blue after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-gradient-to-r after:from-tech-blue after:to-tech-purple hover:after:w-full after:transition-all after:duration-300">技术优势</a>
                        </li>
                        <li><a href="#portfolio"
                                class="relative text-gray-300 font-medium pb-1 hover:text-tech-blue after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-gradient-to-r after:from-tech-blue after:to-tech-purple hover:after:w-full after:transition-all after:duration-300">案例展示</a>
                        </li>
                        <li><a href="#contact"
                                class="relative text-gray-300 font-medium pb-1 hover:text-tech-blue after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-gradient-to-r after:from-tech-blue after:to-tech-purple hover:after:w-full after:transition-all after:duration-300">联系我们</a>
                        </li>
                    </ul>
                </nav>
                <button id="mobile-toggle" class="block md:hidden text-2xl text-tech-blue">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
        </div>
        <!-- 移动端菜单 -->
        <div id="mobile-menu"
            class="fixed inset-0 glass z-40 transform translate-x-full transition-transform duration-500 md:hidden">
            <div class="container mx-auto px-4 py-5">
                <div class="flex justify-between items-center mb-8">
                    <a href="#" class="flex items-center gap-2">
                        <img src="./images/logo.svg" alt="未来基石logo" class="h-10">
                        <span class="text-xl font-bold text-gradient text-gradient-blue">陕西未来基石科技</span>
                    </a>
                    <button id="mobile-close"
                        class="text-2xl text-tech-blue transition-transform duration-300 hover:rotate-90">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <ul class="flex flex-col gap-6">
                    <li><a href="#hero"
                            class="block text-lg font-medium text-tech-blue py-2 border-b border-gray-700">首页</a></li>
                    <li><a href="#about"
                            class="block text-lg font-medium text-gray-300 hover:text-tech-blue py-2 border-b border-gray-700">公司简介</a>
                    </li>
                    <li><a href="#services"
                            class="block text-lg font-medium text-gray-300 hover:text-tech-blue py-2 border-b border-gray-700">业务板块</a>
                    </li>
                    <li><a href="#technology"
                            class="block text-lg font-medium text-gray-300 hover:text-tech-blue py-2 border-b border-gray-700">技术优势</a>
                    </li>
                    <li><a href="#portfolio"
                            class="block text-lg font-medium text-gray-300 hover:text-tech-blue py-2 border-b border-gray-700">案例展示</a>
                    </li>
                    <li><a href="#news"
                            class="block text-lg font-medium text-gray-300 hover:text-tech-blue py-2 border-b border-gray-700">新闻动态</a>
                    </li>
                    <li><a href="#contact"
                            class="block text-lg font-medium text-gray-300 hover:text-tech-blue py-2 border-b border-gray-700">联系我们</a>
                    </li>
                </ul>
            </div>
        </div>
    </header>

    <!-- 首页Hero部分 -->
    <section id="hero"
        class="section bg-dark-400 min-h-screen flex items-center justify-center pt-24 relative code-background">
        <div class="code-flow"></div>
        <!-- 装饰元素 -->
        <div class="floating-shape w-64 h-64 bg-tech-blue/30 top-1/4 right-10 animate-float"></div>
        <div class="floating-shape w-80 h-80 bg-tech-purple/30 bottom-1/4 left-10 animate-pulse-slow"></div>

        <div class="container mx-auto px-4 relative z-10">
            <div class="flex flex-col md:flex-row items-center">
                <div class="w-full md:w-1/2 text-center md:text-left mb-12 md:mb-0 animate-on-scroll animate-fade-in">
                    <div class="shimmer mb-8">
                        <h1 class="text-5xl md:text-6xl font-bold mb-6 leading-tight text-gradient text-gradient-blue">
                            十年匠心<br>重塑数字未来
                        </h1>
                    </div>
                    <p class="text-xl text-gray-300 mb-10 max-w-3xl">
                        西北地区全栈数字化解决方案服务商，专注企业数字化转型，提供专业软件定制开发服务
                    </p>
                    <div class="flex flex-wrap justify-center md:justify-start gap-5 mb-8">
                        <a href="#services-game"
                            class="px-6 py-3 text-base font-semibold text-white rounded-lg shadow-lg btn-gradient btn-gradient-blue hover:shadow-xl hover:shadow-tech-blue/20 transform hover:-translate-y-1 transition-all duration-300">
                            游戏开发 <i class="fas fa-gamepad ml-2"></i>
                        </a>
                        <a href="#services-ecommerce"
                            class="px-6 py-3 text-base font-semibold bg-white/10 text-white rounded-lg shadow-lg hover:bg-white/20 hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300">
                            电商系统 <i class="fas fa-shopping-cart ml-2"></i>
                        </a>
                        <a href="#services-website"
                            class="px-6 py-3 text-base font-semibold bg-white/10 text-white rounded-lg shadow-lg hover:bg-white/20 hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300">
                            企业官网 <i class="fas fa-globe ml-2"></i>
                        </a>
                        <a href="#services-app"
                            class="px-6 py-3 text-base font-semibold bg-white/10 text-white rounded-lg shadow-lg hover:bg-white/20 hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300">
                            APP开发 <i class="fas fa-mobile-alt ml-2"></i>
                        </a>
                    </div>

                    <div class="flex justify-center md:justify-start gap-10 mt-8">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-tech-blue mb-1 counter" data-target="10">0</div>
                            <p class="text-gray-400 text-sm">年技术沉淀</p>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-tech-purple mb-1 counter" data-target="2000">0</div>
                            <p class="text-gray-400 text-sm">成功案例</p>
                        </div>
                    </div>
                </div>
                <div class="w-full md:w-1/2 relative animate-on-scroll animate-slide-up">
                    <div class="relative">
                        <div
                            class="absolute inset-0 bg-gradient-to-br from-tech-blue/20 to-tech-purple/20 rounded-2xl blur-md">
                        </div>
                        <div class="glass rounded-2xl overflow-hidden p-2 relative">
                            <div class="rounded-xl overflow-hidden">
                                <img src="./images/DigitalFuture.jpg" alt="企业数字化转型" class="w-full h-auto">
                            </div>

                            <div
                                class="absolute -right-10 -bottom-10 w-40 h-40 bg-gradient-to-br from-tech-blue to-tech-purple rounded-full blur-2xl opacity-20">
                            </div>
                            <div
                                class="absolute -left-10 -top-10 w-40 h-40 bg-gradient-to-br from-tech-blue to-tech-purple rounded-full blur-2xl opacity-20">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="absolute bottom-10 left-1/2 transform -translate-x-1/2 animate-bounce">
                <a href="#about" class="text-tech-blue text-3xl">
                    <i class="fas fa-chevron-down"></i>
                </a>
            </div>
        </div>
    </section>

    <!-- 公司简介部分 -->
    <section id="about" class="section bg-dark-300 py-20 relative">
        <!-- 装饰元素 -->
        <div class="floating-shape w-72 h-72 bg-tech-purple/20 top-20 right-0 animate-pulse-slow"></div>
        <div class="floating-shape w-64 h-64 bg-tech-blue/20 bottom-20 left-0 animate-float"></div>

        <div class="container mx-auto px-4 py-10 relative z-10">
            <div class="text-center mb-16 animate-on-scroll animate-slide-down">
                <h2 class="text-4xl md:text-5xl font-bold mb-6 inline-block relative text-gradient text-gradient-blue">
                    公司简介
                </h2>
                <p class="text-xl text-gray-400 max-w-2xl mx-auto">西北地区首批全栈数字化解决方案服务商</p>
            </div>

            <div class="flex flex-col lg:flex-row items-start gap-12">
                <div class="w-full lg:w-1/2 animate-on-scroll animate-slide-right">
                    <div class="glass rounded-xl overflow-hidden shadow-xl p-10 shimmer h-full">
                        <div class="flex justify-center mb-8">
                            <img src="./images/logo.svg" alt="陕西未来基石科技" class="h-32 animate-float">
                        </div>
                        <h3 class="text-2xl font-bold text-gradient text-gradient-blue mb-6 text-center">技术驱动创新 · 品质赢得信赖
                        </h3>
                        <div class="space-y-4 text-gray-300">
                            <p>团队组建于于2015年，是一家专注于企业信息化建设与软件定制开发的高新技术企业。</p>
                            <p>经过10年的发展，我们已成功为金融、制造、零售、物流、医疗等多个行业的企业提供了专业软件解决方案，帮助客户实现数字化转型，提升业务效率。</p>
                            <p>作为西北地区全栈数字化解决方案服务商，未来基石科技始终坚持"客户为先，技术创新"的经营理念，不断提升技术实力和服务质量，努力成为企业数字化转型的可靠伙伴。</p>
                        </div>
                    </div>
                </div>
                <div class="w-full lg:w-1/2 animate-on-scroll animate-slide-left">
                    <h3 class="text-2xl font-bold text-gradient text-gradient-blue mb-6">发展历程</h3>
                    <div class="timeline">
                        <div class="timeline-item">
                            <h4 class="text-xl font-bold text-white">2015-2020 技术积累期</h4>
                            <p class="text-gray-400 mt-2">组建技术团队，专注于移动应用和企业管理系统开发，完成了首批30+企业级项目，树立了良好口碑。</p>
                        </div>
                        <div class="timeline-item">
                            <h4 class="text-xl font-bold text-white">2021-2025 行业解决方案标准化</h4>
                            <p class="text-gray-400 mt-2">深入垂直行业，拓展游戏和电商领域业务，建立了标准化解决方案库，技术团队扎实。</p>
                        </div>
                        <div class="timeline-item">
                            <h4 class="text-xl font-bold text-white">2025-至今 成立新品牌 未来基石</h4>
                            <p class="text-gray-400 mt-2">与腾讯云、阿里云等建立战略合作伙伴关系，构建行业解决方案生态圈，技术服务范围扩展至全国，成为西北地区领先的软件开发服务商。
                            </p>
                        </div>
                    </div>

                    <div class="mt-12">
                        <h3 class="text-2xl font-bold text-gradient text-gradient-blue mb-6">专业团队</h3>
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                            <div
                                class="glass p-6 rounded-xl shadow-lg transform hover:scale-105 transition-all duration-300 hover:shadow-xl">
                                <div class="flex items-center gap-4 mb-4">
                                    <div
                                        class="w-16 h-16 rounded-full bg-gradient-to-r from-tech-blue to-tech-purple flex items-center justify-center text-white text-2xl">
                                        <i class="fas fa-code"></i>
                                    </div>
                                    <div>
                                        <h4 class="text-lg font-bold text-white">技术专家</h4>
                                        <p class="text-gray-400 text-sm">CTO - 12年行业经验</p>
                                    </div>
                                </div>
                                <p class="text-gray-400 text-sm">领导技术团队，负责核心架构设计与技术路线规划，拥有多项软件著作权。</p>
                            </div>
                            <div
                                class="glass p-6 rounded-xl shadow-lg transform hover:scale-105 transition-all duration-300 hover:shadow-xl">
                                <div class="flex items-center gap-4 mb-4">
                                    <div
                                        class="w-16 h-16 rounded-full bg-gradient-to-r from-tech-purple to-tech-blue flex items-center justify-center text-white text-2xl">
                                        <i class="fas fa-gamepad"></i>
                                    </div>
                                    <div>
                                        <h4 class="text-lg font-bold text-white">游戏开发负责人</h4>
                                        <p class="text-gray-400 text-sm">8年游戏开发经验</p>
                                    </div>
                                </div>
                                <p class="text-gray-400 text-sm">曾参与多款爆款手游研发，精通Unity/Unreal引擎，深耕游戏架构与优化。</p>
                            </div>
                            <div
                                class="glass p-6 rounded-xl shadow-lg transform hover:scale-105 transition-all duration-300 hover:shadow-xl">
                                <div class="flex items-center gap-4 mb-4">
                                    <div
                                        class="w-16 h-16 rounded-full bg-gradient-to-r from-tech-blue to-tech-purple flex items-center justify-center text-white text-2xl">
                                        <i class="fas fa-shopping-cart"></i>
                                    </div>
                                    <div>
                                        <h4 class="text-lg font-bold text-white">电商技术负责人</h4>
                                        <p class="text-gray-400 text-sm">10年电商平台开发经验</p>
                                    </div>
                                </div>
                                <p class="text-gray-400 text-sm">专注电商系统架构设计，精通高并发、大数据处理，为多家电商企业提供技术支持。</p>
                            </div>
                            <div
                                class="glass p-6 rounded-xl shadow-lg transform hover:scale-105 transition-all duration-300 hover:shadow-xl">
                                <div class="flex items-center gap-4 mb-4">
                                    <div
                                        class="w-16 h-16 rounded-full bg-gradient-to-r from-tech-purple to-tech-blue flex items-center justify-center text-white text-2xl">
                                        <i class="fas fa-mobile-alt"></i>
                                    </div>
                                    <div>
                                        <h4 class="text-lg font-bold text-white">移动开发负责人</h4>
                                        <p class="text-gray-400 text-sm">9年移动应用开发经验</p>
                                    </div>
                                </div>
                                <p class="text-gray-400 text-sm">精通Flutter跨平台开发，物联网应用开发专家，负责公司移动应用技术团队管理。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 业务板块部分 -->
    <section id="services" class="section bg-dark-400 py-20 relative">
        <!-- 装饰元素 -->
        <div class="floating-shape w-80 h-80 bg-tech-blue/20 top-20 left-10 animate-pulse-slow"></div>
        <div class="floating-shape w-72 h-72 bg-tech-purple/20 bottom-20 right-10 animate-float"></div>

        <div class="container mx-auto px-4 py-10 relative z-10">
            <div class="text-center mb-16 animate-on-scroll animate-slide-down">
                <h2 class="text-4xl md:text-5xl font-bold mb-6 inline-block relative text-gradient text-gradient-blue">
                    业务板块
                </h2>
                <p class="text-xl text-gray-400 max-w-2xl mx-auto">专业的定制软件开发服务，以创新科技推动企业成长</p>
            </div>

            <!-- 游戏开发板块 -->
            <div id="services-game" class="mb-24">
                <div class="flex flex-col lg:flex-row items-center gap-12">
                    <div class="w-full lg:w-1/2 animate-on-scroll animate-slide-right">
                        <div class="relative">
                            <div
                                class="absolute inset-0 bg-gradient-to-br from-tech-blue/20 to-tech-purple/20 rounded-2xl blur-md">
                            </div>
                            <div class="glass rounded-2xl overflow-hidden p-2 relative">
                                <div class="rounded-xl overflow-hidden">
                                    <img src="./images/GameDevelopment.png" alt="游戏开发" class="w-full h-auto">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="w-full lg:w-1/2 animate-on-scroll animate-slide-left">
                        <h3 class="text-3xl font-bold text-gradient text-gradient-blue mb-6 flex items-center gap-3">
                            <i class="fas fa-gamepad"></i> 游戏开发
                        </h3>
                        <p class="text-gray-300 mb-8">我们拥有专业的游戏开发团队，提供从概念设计到上线运营的全流程游戏开发服务，助力客户打造精品游戏产品。</p>

                        <div class="space-y-6">
                            <div class="glass p-6 rounded-xl hover-card">
                                <h4 class="text-xl font-bold mb-3 text-white">手游/H5游戏全流程开发</h4>
                                <p class="text-gray-400 mb-4">提供从游戏策划、UI设计、美术制作、程序开发到上线发行的一站式服务，为客户打造具有竞争力的游戏产品。</p>
                                <div class="flex flex-wrap gap-2">
                                    <span
                                        class="px-3 py-1 text-xs bg-tech-blue/20 text-tech-blue rounded-full">休闲游戏</span>
                                    <span
                                        class="px-3 py-1 text-xs bg-tech-blue/20 text-tech-blue rounded-full">策略游戏</span>
                                    <span
                                        class="px-3 py-1 text-xs bg-tech-blue/20 text-tech-blue rounded-full">RPG</span>
                                    <span
                                        class="px-3 py-1 text-xs bg-tech-blue/20 text-tech-blue rounded-full">H5小游戏</span>
                                </div>
                            </div>

                            <div class="glass p-6 rounded-xl hover-card">
                                <h4 class="text-xl font-bold mb-3 text-white">Unity/Unreal引擎定制</h4>
                                <p class="text-gray-400 mb-4">基于Unity、Unreal引擎进行定制开发，提供性能优化、特效实现、多平台适配等专业服务。</p>
                                <div class="flex items-center mt-2 gap-4">
                                    <div class="flex items-center">
                                        <i class="fas fa-mobile-alt text-tech-blue mr-2"></i>
                                        <span class="text-gray-300 text-sm">全平台适配</span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-tachometer-alt text-tech-blue mr-2"></i>
                                        <span class="text-gray-300 text-sm">高性能保障</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 其他业务板块已省略... -->

        </div>
    </section>

    <!-- 技术优势部分 -->
    <section id="technology" class="section bg-dark-300 py-20 relative">
        <!-- 装饰元素 -->
        <div class="floating-shape w-72 h-72 bg-tech-purple/20 top-20 right-0 animate-pulse-slow"></div>
        <div class="floating-shape w-64 h-64 bg-tech-blue/20 bottom-20 left-0 animate-float"></div>

        <div class="container mx-auto px-4 py-10 relative z-10">
            <div class="text-center mb-16 animate-on-scroll animate-slide-down">
                <h2 class="text-4xl md:text-5xl font-bold mb-6 inline-block relative text-gradient text-gradient-blue">
                    技术优势
                </h2>
                <p class="text-xl text-gray-400 max-w-2xl mx-auto">专业的技术团队，先进的技术栈，为您的项目保驾护航</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- 技术优势卡片1 -->
                <div class="glass p-8 rounded-xl hover-card animate-on-scroll animate-slide-up">
                    <div
                        class="w-16 h-16 rounded-full bg-gradient-to-r from-tech-blue to-tech-purple flex items-center justify-center text-white text-2xl mb-6">
                        <i class="fas fa-code"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-white mb-4">全栈技术团队</h3>
                    <p class="text-gray-400 mb-4">拥有涵盖前端、后端、移动端、游戏开发、运维等全方位的技术团队，能够提供一站式开发服务。</p>
                    <div class="flex flex-wrap gap-2 mt-5">
                        <span
                            class="px-3 py-1 text-xs bg-tech-blue/10 text-gray-300 rounded-full border border-tech-blue/30">React</span>
                        <span
                            class="px-3 py-1 text-xs bg-tech-blue/10 text-gray-300 rounded-full border border-tech-blue/30">Vue</span>
                        <span
                            class="px-3 py-1 text-xs bg-tech-blue/10 text-gray-300 rounded-full border border-tech-blue/30">Node.js</span>
                        <span
                            class="px-3 py-1 text-xs bg-tech-blue/10 text-gray-300 rounded-full border border-tech-blue/30">Flutter</span>
                        <span
                            class="px-3 py-1 text-xs bg-tech-blue/10 text-gray-300 rounded-full border border-tech-blue/30">Unity</span>
                    </div>
                </div>

                <!-- 技术优势卡片2 -->
                <div class="glass p-8 rounded-xl hover-card animate-on-scroll animate-slide-up"
                    style="animation-delay: 0.2s;">
                    <div
                        class="w-16 h-16 rounded-full bg-gradient-to-r from-tech-purple to-tech-blue flex items-center justify-center text-white text-2xl mb-6">
                        <i class="fas fa-server"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-white mb-4">高性能架构</h3>
                    <p class="text-gray-400 mb-4">采用微服务架构设计，支持分布式部署与容器化，确保系统高可用性和可扩展性。</p>
                    <div class="flex flex-wrap gap-2 mt-5">
                        <span
                            class="px-3 py-1 text-xs bg-tech-purple/10 text-gray-300 rounded-full border border-tech-purple/30">微服务</span>
                        <span
                            class="px-3 py-1 text-xs bg-tech-purple/10 text-gray-300 rounded-full border border-tech-purple/30">Docker</span>
                        <span
                            class="px-3 py-1 text-xs bg-tech-purple/10 text-gray-300 rounded-full border border-tech-purple/30">Kubernetes</span>
                        <span
                            class="px-3 py-1 text-xs bg-tech-purple/10 text-gray-300 rounded-full border border-tech-purple/30">负载均衡</span>
                    </div>
                </div>

                <!-- 技术优势卡片3 -->
                <div class="glass p-8 rounded-xl hover-card animate-on-scroll animate-slide-up"
                    style="animation-delay: 0.4s;">
                    <div
                        class="w-16 h-16 rounded-full bg-gradient-to-r from-tech-blue to-tech-purple flex items-center justify-center text-white text-2xl mb-6">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-white mb-4">安全可靠</h3>
                    <p class="text-gray-400 mb-4">严格遵循安全开发规范，采用多重加密和防护措施，保障系统安全与数据隐私。</p>
                    <div class="flex flex-wrap gap-2 mt-5">
                        <span
                            class="px-3 py-1 text-xs bg-tech-blue/10 text-gray-300 rounded-full border border-tech-blue/30">HTTPS</span>
                        <span
                            class="px-3 py-1 text-xs bg-tech-blue/10 text-gray-300 rounded-full border border-tech-blue/30">数据加密</span>
                        <span
                            class="px-3 py-1 text-xs bg-tech-blue/10 text-gray-300 rounded-full border border-tech-blue/30">安全审计</span>
                        <span
                            class="px-3 py-1 text-xs bg-tech-blue/10 text-gray-300 rounded-full border border-tech-blue/30">权限控制</span>
                    </div>
                </div>

                <!-- 技术优势卡片4 -->
                <div class="glass p-8 rounded-xl hover-card animate-on-scroll animate-slide-up">
                    <div
                        class="w-16 h-16 rounded-full bg-gradient-to-r from-tech-purple to-tech-blue flex items-center justify-center text-white text-2xl mb-6">
                        <i class="fas fa-tachometer-alt"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-white mb-4">高效开发流程</h3>
                    <p class="text-gray-400 mb-4">采用敏捷开发方法论，持续集成/持续部署(CI/CD)，确保开发效率与质量。</p>
                    <div class="flex flex-wrap gap-2 mt-5">
                        <span
                            class="px-3 py-1 text-xs bg-tech-purple/10 text-gray-300 rounded-full border border-tech-purple/30">敏捷开发</span>
                        <span
                            class="px-3 py-1 text-xs bg-tech-purple/10 text-gray-300 rounded-full border border-tech-purple/30">CI/CD</span>
                        <span
                            class="px-3 py-1 text-xs bg-tech-purple/10 text-gray-300 rounded-full border border-tech-purple/30">自动化测试</span>
                        <span
                            class="px-3 py-1 text-xs bg-tech-purple/10 text-gray-300 rounded-full border border-tech-purple/30">代码审查</span>
                    </div>
                </div>

                <!-- 技术优势卡片5 -->
                <div class="glass p-8 rounded-xl hover-card animate-on-scroll animate-slide-up"
                    style="animation-delay: 0.2s;">
                    <div
                        class="w-16 h-16 rounded-full bg-gradient-to-r from-tech-blue to-tech-purple flex items-center justify-center text-white text-2xl mb-6">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-white mb-4">数据分析能力</h3>
                    <p class="text-gray-400 mb-4">拥有专业的数据分析团队，提供数据可视化、智能推荐、用户行为分析等服务。</p>
                    <div class="flex flex-wrap gap-2 mt-5">
                        <span
                            class="px-3 py-1 text-xs bg-tech-blue/10 text-gray-300 rounded-full border border-tech-blue/30">大数据</span>
                        <span
                            class="px-3 py-1 text-xs bg-tech-blue/10 text-gray-300 rounded-full border border-tech-blue/30">机器学习</span>
                        <span
                            class="px-3 py-1 text-xs bg-tech-blue/10 text-gray-300 rounded-full border border-tech-blue/30">数据可视化</span>
                        <span
                            class="px-3 py-1 text-xs bg-tech-blue/10 text-gray-300 rounded-full border border-tech-blue/30">智能推荐</span>
                    </div>
                </div>

                <!-- 技术优势卡片6 -->
                <div class="glass p-8 rounded-xl hover-card animate-on-scroll animate-slide-up"
                    style="animation-delay: 0.4s;">
                    <div
                        class="w-16 h-16 rounded-full bg-gradient-to-r from-tech-purple to-tech-blue flex items-center justify-center text-white text-2xl mb-6">
                        <i class="fas fa-users-cog"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-white mb-4">优质技术支持</h3>
                    <p class="text-gray-400 mb-4">提供7*24小时技术支持服务，定期系统维护与更新，确保系统稳定运行。</p>
                    <div class="flex flex-wrap gap-2 mt-5">
                        <span
                            class="px-3 py-1 text-xs bg-tech-purple/10 text-gray-300 rounded-full border border-tech-purple/30">7*24服务</span>
                        <span
                            class="px-3 py-1 text-xs bg-tech-purple/10 text-gray-300 rounded-full border border-tech-purple/30">定期维护</span>
                        <span
                            class="px-3 py-1 text-xs bg-tech-purple/10 text-gray-300 rounded-full border border-tech-purple/30">故障监控</span>
                        <span
                            class="px-3 py-1 text-xs bg-tech-purple/10 text-gray-300 rounded-full border border-tech-purple/30">技术培训</span>
                    </div>
                </div>
            </div>

            <!-- 技术合作伙伴 -->
            <div class="mt-20">
                <h3 class="text-2xl font-bold text-white mb-10 text-center">技术合作伙伴</h3>
                <div class="flex flex-wrap justify-center items-center gap-8 md:gap-16">
                    <div
                        class="w-32 h-20 bg-white/5 rounded-lg flex items-center justify-center p-4 transition-all hover:bg-white/10">
                        <img src="./images/腾讯云 -copy.png" alt="腾讯云" class="max-w-full max-h-full">
                    </div>
                    <div
                        class="w-32 h-20 bg-white/5 rounded-lg flex items-center justify-center p-4 transition-all hover:bg-white/10">
                        <img src="./images/阿里云官方-中文LOGO.png" alt="阿里云" class="max-w-full max-h-full">
                    </div>
                    <div
                        class="w-32 h-20 bg-white/5 rounded-lg flex items-center justify-center p-4 transition-all hover:bg-white/10">
                        <img src="./images/unity.png" alt="Unity" class="max-w-full max-h-full">
                    </div>
                    <div
                        class="w-32 h-20 bg-white/5 rounded-lg flex items-center justify-center p-4 transition-all hover:bg-white/10">
                        <img src="./images/unrealengine.png" alt="Unreal" class="max-w-full max-h-full">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 案例展示部分 -->
    <section id="portfolio" class="section bg-dark-400 py-20 relative">
        <!-- 装饰元素 -->
        <div class="floating-shape w-80 h-80 bg-tech-blue/20 top-20 left-10 animate-pulse-slow"></div>
        <div class="floating-shape w-72 h-72 bg-tech-purple/20 bottom-20 right-10 animate-float"></div>

        <div class="container mx-auto px-4 py-10 relative z-10">
            <div class="text-center mb-16 animate-on-scroll animate-slide-down">
                <h2 class="text-4xl md:text-5xl font-bold mb-6 inline-block relative text-gradient text-gradient-blue">
                    案例展示
                </h2>
                <p class="text-xl text-gray-400 max-w-2xl mx-auto">我们的成功案例，展示我们的专业能力与服务品质</p>
            </div>

            <!-- 案例分类过滤器 -->
            <div class="flex flex-wrap justify-center gap-4 mb-12">
                <button class="px-5 py-2 rounded-lg bg-tech-blue text-white">全部</button>
                <button
                    class="px-5 py-2 rounded-lg bg-dark-100 text-gray-300 hover:bg-tech-blue/20 transition-all">游戏开发</button>
                <button
                    class="px-5 py-2 rounded-lg bg-dark-100 text-gray-300 hover:bg-tech-blue/20 transition-all">电商系统</button>
                <button
                    class="px-5 py-2 rounded-lg bg-dark-100 text-gray-300 hover:bg-tech-blue/20 transition-all">企业官网</button>
                <button
                    class="px-5 py-2 rounded-lg bg-dark-100 text-gray-300 hover:bg-tech-blue/20 transition-all">移动应用</button>
            </div>

            <!-- 案例展示网格 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- 案例卡片1 -->
                <div class="glass rounded-xl overflow-hidden group animate-on-scroll animate-fade-in">
                    <div class="relative overflow-hidden">
                        <img src="./images/17edc1c2-b02d-4372-bc02-889795852911_1743670731231360511_origin~tplv-a9rns2rl98-web-thumb.jpeg"
                            alt="休闲消除游戏"
                            class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110">
                        <div
                            class="absolute inset-0 bg-gradient-to-t from-dark-400 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-6">
                            <h3 class="text-xl font-bold text-white mb-2">休闲消除游戏</h3>
                            <p class="text-gray-300 text-sm">为某知名品牌开发的休闲类手游，日活跃用户超30万</p>
                            <div class="mt-4">
                                <span class="px-3 py-1 text-xs bg-tech-blue/20 text-tech-blue rounded-full">Unity</span>
                                <span
                                    class="px-3 py-1 text-xs bg-tech-blue/20 text-tech-blue rounded-full ml-2">游戏开发</span>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-white mb-2">休闲消除游戏</h3>
                        <p class="text-gray-400 text-sm line-clamp-2">
                            为某知名品牌开发的休闲类手游，完整实现了游戏玩法、社交系统、支付系统等功能，上线后日活跃用户超30万。</p>
                    </div>
                </div>

                <!-- 案例卡片2 -->
                <div class="glass rounded-xl overflow-hidden group animate-on-scroll animate-fade-in"
                    style="animation-delay: 0.2s;">
                    <div class="relative overflow-hidden">
                        <img src="./images/c792bd65-cb8c-4a36-86bf-77527a3f3f2f_1743670748580930987_origin~tplv-a9rns2rl98-web-thumb.jpeg"
                            alt="跨境电商平台"
                            class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110">
                        <div
                            class="absolute inset-0 bg-gradient-to-t from-dark-400 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-6">
                            <h3 class="text-xl font-bold text-white mb-2">跨境电商平台</h3>
                            <p class="text-gray-300 text-sm">为某外贸公司打造的跨境电商独立站，支持多语言多货币</p>
                            <div class="mt-4">
                                <span
                                    class="px-3 py-1 text-xs bg-tech-purple/20 text-tech-purple rounded-full">React</span>
                                <span
                                    class="px-3 py-1 text-xs bg-tech-purple/20 text-tech-purple rounded-full ml-2">电商系统</span>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-white mb-2">跨境电商平台</h3>
                        <p class="text-gray-400 text-sm line-clamp-2">为某外贸公司打造的跨境电商独立站，支持多语言多货币，集成物流跟踪、在线支付等功能。</p>
                    </div>
                </div>

                <!-- 案例卡片3 -->
                <div class="glass rounded-xl overflow-hidden group animate-on-scroll animate-fade-in"
                    style="animation-delay: 0.4s;">
                    <div class="relative overflow-hidden">
                        <img src="./images/2c0f2e9d-379f-4dc6-a7b2-5997f921f129_1743670760834495043_origin~tplv-a9rns2rl98-web-thumb.jpeg"
                            alt="智能家居控制APP"
                            class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110">
                        <div
                            class="absolute inset-0 bg-gradient-to-t from-dark-400 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-6">
                            <h3 class="text-xl font-bold text-white mb-2">智能家居控制APP</h3>
                            <p class="text-gray-300 text-sm">为某智能家居品牌开发的移动控制应用，支持iOS/Android双平台</p>
                            <div class="mt-4">
                                <span
                                    class="px-3 py-1 text-xs bg-tech-blue/20 text-tech-blue rounded-full">Flutter</span>
                                <span
                                    class="px-3 py-1 text-xs bg-tech-blue/20 text-tech-blue rounded-full ml-2">移动应用</span>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-white mb-2">智能家居控制APP</h3>
                        <p class="text-gray-400 text-sm line-clamp-2">为某智能家居品牌开发的移动控制应用，支持WiFi、蓝牙等多种连接方式，实现智能设备远程监控。</p>
                    </div>
                </div>

                <!-- 案例卡片4 -->
                <div class="glass rounded-xl overflow-hidden group animate-on-scroll animate-fade-in">
                    <div class="relative overflow-hidden">
                        <img src="./images/fe2eb264-724a-4bda-b14a-7090d3698564_1743670998605721867_origin~tplv-a9rns2rl98-web-thumb.jpeg"
                            alt="制造业集团官网"
                            class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110">
                        <div
                            class="absolute inset-0 bg-gradient-to-t from-dark-400 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-6">
                            <h3 class="text-xl font-bold text-white mb-2">制造业集团官网</h3>
                            <p class="text-gray-300 text-sm">为某大型制造业集团打造的企业官网与后台管理系统</p>
                            <div class="mt-4">
                                <span
                                    class="px-3 py-1 text-xs bg-tech-purple/20 text-tech-purple rounded-full">Vue</span>
                                <span
                                    class="px-3 py-1 text-xs bg-tech-purple/20 text-tech-purple rounded-full ml-2">企业官网</span>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-white mb-2">制造业集团官网</h3>
                        <p class="text-gray-400 text-sm line-clamp-2">为某大型制造业集团打造的企业官网与后台管理系统，支持多语言国际化，集成产品展示、新闻动态等模块。
                        </p>
                    </div>
                </div>

                <!-- 案例卡片5 -->
                <div class="glass rounded-xl overflow-hidden group animate-on-scroll animate-fade-in"
                    style="animation-delay: 0.2s;">
                    <div class="relative overflow-hidden">
                        <img src="./images/313d8501-3ca3-402a-97db-45fcca8c3405_1743671013993937649_origin~tplv-a9rns2rl98-web-thumb.jpeg"
                            alt="多人在线RPG"
                            class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110">
                        <div
                            class="absolute inset-0 bg-gradient-to-t from-dark-400 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-6">
                            <h3 class="text-xl font-bold text-white mb-2">多人在线RPG</h3>
                            <p class="text-gray-300 text-sm">精品角色扮演游戏，支持多人在线互动，月流水超280万</p>
                            <div class="mt-4">
                                <span
                                    class="px-3 py-1 text-xs bg-tech-blue/20 text-tech-blue rounded-full">Unreal</span>
                                <span
                                    class="px-3 py-1 text-xs bg-tech-blue/20 text-tech-blue rounded-full ml-2">游戏开发</span>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-white mb-2">多人在线RPG</h3>
                        <p class="text-gray-400 text-sm line-clamp-2">基于Unreal引擎开发的精品角色扮演游戏，支持多人在线互动，云服务器集群部署，月流水超280万。
                        </p>
                    </div>
                </div>

                <!-- 案例卡片6 -->
                <div class="glass rounded-xl overflow-hidden group animate-on-scroll animate-fade-in"
                    style="animation-delay: 0.4s;">
                    <div class="relative overflow-hidden">
                        <img src="./images/835ed968-c8eb-4bbc-8c06-f7d068650ade_1743671081724612974_origin~tplv-a9rns2rl98-web-thumb.jpeg"
                            alt="医疗健康APP"
                            class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110">
                        <div
                            class="absolute inset-0 bg-gradient-to-t from-dark-400 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-6">
                            <h3 class="text-xl font-bold text-white mb-2">医疗健康APP</h3>
                            <p class="text-gray-300 text-sm">面向医疗机构的患者管理与健康监测APP</p>
                            <div class="mt-4">
                                <span
                                    class="px-3 py-1 text-xs bg-tech-blue/20 text-tech-blue rounded-full">Flutter</span>
                                <span
                                    class="px-3 py-1 text-xs bg-tech-blue/20 text-tech-blue rounded-full ml-2">移动应用</span>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-white mb-2">医疗健康APP</h3>
                        <p class="text-gray-400 text-sm line-clamp-2">面向医疗机构的患者管理与健康监测APP，支持患者在线问诊、预约挂号、健康数据上传与分析等功能。
                        </p>
                    </div>
                </div>
            </div>

            <!-- 查看更多按钮 -->
            <!-- <div class="text-center mt-12">
                <a href="#" class="inline-block px-8 py-3 text-base font-semibold rounded-lg shadow-lg btn-gradient btn-gradient-blue hover:shadow-xl hover:shadow-tech-blue/20 transform hover:-translate-y-1 transition-all duration-300">
                    查看更多案例 <i class="fas fa-chevron-right ml-2"></i>
                </a>
            </div> -->
        </div>
    </section>

    <!-- 联系我们部分 -->
    <section id="contact" class="section bg-dark-400 py-20 relative">
        <!-- 装饰元素 -->
        <div class="floating-shape w-80 h-80 bg-tech-blue/20 top-20 left-10 animate-pulse-slow"></div>
        <div class="floating-shape w-72 h-72 bg-tech-purple/20 bottom-20 right-10 animate-float"></div>

        <div class="container mx-auto px-4 py-10 relative z-10">
            <div class="text-center mb-16 animate-on-scroll animate-slide-down">
                <h2 class="text-4xl md:text-5xl font-bold mb-6 inline-block relative text-gradient text-gradient-blue">
                    联系我们
                </h2>
                <p class="text-xl text-gray-400 max-w-2xl mx-auto">让我们一起探讨您的项目需求，开启数字化转型之旅</p>
            </div>

            <div class="flex flex-col lg:flex-row gap-12">
                <div class="w-full lg:w-1/2 animate-on-scroll animate-slide-right">
                    <div class="glass rounded-xl overflow-hidden p-8">
                        <h3 class="text-2xl font-bold text-white mb-6">项目咨询</h3>
                        <form onsubmit="alert('提交成功！');return false" method="POST" class="space-y-6">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-300 mb-2">您的姓名</label>
                                <input type="text" id="name" name="name"
                                    class="w-full px-4 py-3 bg-dark-100 text-white border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-tech-blue focus:border-transparent">
                            </div>
                            <div>
                                <label for="company" class="block text-sm font-medium text-gray-300 mb-2">公司名称</label>
                                <input type="text" id="company" name="company"
                                    class="w-full px-4 py-3 bg-dark-100 text-white border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-tech-blue focus:border-transparent">
                            </div>
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-300 mb-2">电子邮箱</label>
                                <input type="email" id="email" name="email"
                                    class="w-full px-4 py-3 bg-dark-100 text-white border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-tech-blue focus:border-transparent">
                            </div>
                            <div>
                                <label for="phone" class="block text-sm font-medium text-gray-300 mb-2">联系电话</label>
                                <input type="tel" id="phone" name="phone"
                                    class="w-full px-4 py-3 bg-dark-100 text-white border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-tech-blue focus:border-transparent">
                            </div>
                            <div>
                                <label for="message" class="block text-sm font-medium text-gray-300 mb-2">项目需求</label>
                                <textarea id="message" name="message" rows="4"
                                    class="w-full px-4 py-3 bg-dark-100 text-white border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-tech-blue focus:border-transparent"></textarea>
                            </div>
                            <div>
                                <button type="submit"
                                    class="w-full px-6 py-3 text-base font-semibold text-white rounded-lg shadow-lg btn-gradient btn-gradient-blue hover:shadow-xl hover:shadow-tech-blue/20 transform hover:-translate-y-1 transition-all duration-300">
                                    提交咨询 <i class="fas fa-paper-plane ml-2"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="w-full lg:w-1/2 animate-on-scroll animate-slide-left">
                    <div class="glass rounded-xl overflow-hidden p-8 h-full">
                        <h3 class="text-2xl font-bold text-white mb-6">联系方式</h3>
                        <div class="space-y-8">
                            <div class="flex items-start gap-4">
                                <div
                                    class="w-12 h-12 rounded-full bg-gradient-to-r from-tech-blue to-tech-purple flex-shrink-0 flex items-center justify-center text-white text-xl">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <div>
                                    <h4 class="text-lg font-semibold text-white mb-1">公司地址</h4>
                                    <p class="text-gray-400">陕西省西安市莲湖区西华门1号凯爱大厦B座5层501C-G49室</p>
                                </div>
                            </div>

                            <div class="flex items-start gap-4">
                                <div
                                    class="w-12 h-12 rounded-full bg-gradient-to-r from-tech-blue to-tech-purple flex-shrink-0 flex items-center justify-center text-white text-xl">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                <div>
                                    <h4 class="text-lg font-semibold text-white mb-1">电子邮箱</h4>
                                    <p class="text-gray-400"><EMAIL></p>
                                    <p class="text-gray-400"><EMAIL></p>
                                </div>
                            </div>

                            <div class="flex items-start gap-4">
                                <div
                                    class="w-12 h-12 rounded-full bg-gradient-to-r from-tech-blue to-tech-purple flex-shrink-0 flex items-center justify-center text-white text-xl">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div>
                                    <h4 class="text-lg font-semibold text-white mb-1">工作时间</h4>
                                    <p class="text-gray-400">周一至周五：9:00 - 18:00</p>
                                    <p class="text-gray-400">节假日休息（技术支持24小时在线）</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="bg-dark-400 py-12 relative">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10">
                <div>
                    <div class="flex items-center gap-2 mb-6">
                        <img src="./images/logo.svg" alt="未来基石logo" class="h-10">
                        <span class="text-xl font-bold text-gradient text-gradient-blue">陕西未来基石科技</span>
                    </div>
                    <p class="text-gray-400 text-sm mb-6">西北地区首批全栈数字化解决方案服务商，专注企业数字化转型，提供专业软件定制开发服务。</p>
                    <!-- <div class="flex gap-4">
                    <a href="#" class="text-gray-400 hover:text-tech-blue transition-colors">
                        <i class="fab fa-weixin text-xl"></i>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-tech-blue transition-colors">
                        <i class="fab fa-weibo text-xl"></i>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-tech-blue transition-colors">
                        <i class="fab fa-qq text-xl"></i>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-tech-blue transition-colors">
                        <i class="fab fa-bilibili text-xl"></i>
                    </a>
                </div> -->
                </div>

                <div>
                    <h4 class="text-white font-bold text-lg mb-6">业务导航</h4>
                    <ul class="space-y-3">
                        <li><a href="#services-game"
                                class="text-gray-400 hover:text-tech-blue transition-colors">游戏开发</a></li>
                        <li><a href="#services-ecommerce"
                                class="text-gray-400 hover:text-tech-blue transition-colors">电商系统</a></li>
                        <li><a href="#services-website"
                                class="text-gray-400 hover:text-tech-blue transition-colors">企业官网</a></li>
                        <li><a href="#services-app"
                                class="text-gray-400 hover:text-tech-blue transition-colors">移动应用</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="text-white font-bold text-lg mb-6">关于我们</h4>
                    <ul class="space-y-3">
                        <li><a href="#about" class="text-gray-400 hover:text-tech-blue transition-colors">公司简介</a></li>
                        <li><a href="#technology" class="text-gray-400 hover:text-tech-blue transition-colors">技术优势</a>
                        </li>
                        <li><a href="#portfolio" class="text-gray-400 hover:text-tech-blue transition-colors">成功案例</a>
                        </li>
                    </ul>
                </div>

                <div>
                    <h4 class="text-white font-bold text-lg mb-6">联系我们</h4>
                    <ul class="space-y-3">
                        <li class="flex items-start gap-2">
                            <i class="fas fa-map-marker-alt text-tech-blue mt-1"></i>
                            <span class="text-gray-400">陕西省西安市莲湖区西华门1号凯爱大厦B座5层501C-G49室</span>
                        </li>
                        <li class="flex items-center gap-2">
                            <i class="fas fa-envelope text-tech-blue"></i>
                            <span class="text-gray-400"><EMAIL></span>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="mt-12 pt-8 border-t border-gray-700 text-center text-gray-500 text-sm">
                <p>© 2015-2025 陕西未来基石科技有限公司 版权所有 | <a href="https://beian.miit.gov.cn/#/Integrated/index"
                        target="_blank" class="hover:text-tech-blue transition-colors">陕ICP备2025065748号-1</a></p>
            </div>
        </div>
    </footer>

    <!-- 回到顶部按钮 -->
    <button id="back-to-top"
        class="fixed right-6 bottom-6 w-12 h-12 rounded-full bg-gradient-to-r from-tech-blue to-tech-purple text-white flex items-center justify-center shadow-lg z-50 transition-all opacity-0 invisible hover:shadow-xl hover:scale-110">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- JavaScript 部分 -->
    <script>
        // 导航栏滚动效果
        window.addEventListener('scroll', function () {
            const header = document.getElementById('header');
            const scrollPosition = window.scrollY;

            if (scrollPosition > 100) {
                header.classList.add('py-2');
                header.classList.remove('py-4');
            } else {
                header.classList.add('py-4');
                header.classList.remove('py-2');
            }
        });

        // 移动端菜单
        document.getElementById('mobile-toggle').addEventListener('click', function () {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.remove('translate-x-full');
        });

        document.getElementById('mobile-close').addEventListener('click', function () {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.add('translate-x-full');
        });

        // 滚动动画
        const observerOptions = {
            root: null,
            rootMargin: '0px',
            threshold: 0.1
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animated');
                }
            });
        }, observerOptions);

        document.addEventListener('DOMContentLoaded', function () {
            // 监听所有带有动画的元素
            const animatedElements = document.querySelectorAll('.animate-on-scroll');
            animatedElements.forEach(element => {
                observer.observe(element);
            });

            // 数字计数动画
            const counters = document.querySelectorAll('.counter');
            counters.forEach(counter => {
                const target = parseInt(counter.getAttribute('data-target'));
                const duration = 2000; // 动画持续时间（毫秒）
                const stepTime = 50; // 每步时间（毫秒）
                const steps = duration / stepTime;
                const increment = target / steps;
                let current = 0;

                const updateCounter = () => {
                    current += increment;
                    if (current < target) {
                        counter.textContent = Math.ceil(current);
                        setTimeout(updateCounter, stepTime);
                    } else {
                        counter.textContent = target;
                    }
                };

                updateCounter();
            });

            // 回到顶部按钮
            const backToTopButton = document.getElementById('back-to-top');
            window.addEventListener('scroll', function () {
                if (window.scrollY > 500) {
                    backToTopButton.classList.remove('opacity-0', 'invisible');
                    backToTopButton.classList.add('opacity-100', 'visible');
                } else {
                    backToTopButton.classList.remove('opacity-100', 'visible');
                    backToTopButton.classList.add('opacity-0', 'invisible');
                }
            });

            backToTopButton.addEventListener('click', function () {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>

</html>