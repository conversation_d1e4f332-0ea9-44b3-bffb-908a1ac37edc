<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>信息差 - 高保真移动应用</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#6366F1',
                        'primary-light': '#818CF8',
                        'primary-dark': '#4F46E5',
                        secondary: '#EC4899',
                        'secondary-light': '#F472B6',
                        accent: '#10B981',
                        'accent-light': '#34D399',
                        success: '#059669',
                        warning: '#F59E0B',
                        danger: '#EF4444',
                        'bg-primary': '#FAFAFB',
                        'bg-secondary': '#F8FAFC',
                        'bg-card': '#FFFFFF',
                        'text-primary': '#1F2937',
                        'text-secondary': '#6B7280',
                        'text-muted': '#9CA3AF',
                        'border-light': '#E5E7EB',
                        'border-medium': '#D1D5DB',
                        gray: {
                            50: '#F9FAFB',
                            100: '#F3F4F6',
                            200: '#E5E7EB',
                            300: '#D1D5DB',
                            400: '#9CA3AF',
                            500: '#6B7280',
                            600: '#4B5563',
                            700: '#374151',
                            800: '#1F2937',
                            900: '#111827'
                        }
                    },
                    fontFamily: {
                        'sf': ['-apple-system', 'BlinkMacSystemFont', 'SF Pro Display', 'Segoe UI', 'Roboto', 'sans-serif']
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-out',
                        'slide-up': 'slideUp 0.3s ease-out',
                        'bounce-gentle': 'bounceGentle 2s infinite',
                        'pulse-slow': 'pulse 3s infinite'
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' }
                        },
                        bounceGentle: {
                            '0%, 100%': { transform: 'translateY(0)' },
                            '50%': { transform: 'translateY(-10px)' }
                        }
                    }
                }
            }
        }
    </script>
    <style>
        /* 移动端适配 */
        .mobile-container {
            width: 393px;
            height: 852px;
            margin: 20px auto;
            border-radius: 24px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            position: relative;
            background: #000;
            display: inline-block;
            vertical-align: top;
        }

        .screen {
            width: 100%;
            height: 100%;
            background: #fff;
            position: relative;
            overflow: hidden;
        }

        /* 页面容器 */
        .pages-container {
            display: flex;
            flex-wrap: wrap;
            gap: 40px;
            justify-content: center;
            padding: 20px;
        }

        /* 状态栏 */
        .status-bar {
            height: 44px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 13px;
            font-weight: 600;
        }

        /* 安全区域 */
        .safe-area {
            height: calc(100% - 44px - 34px);
            overflow-y: auto;
            background: linear-gradient(180deg, #FAFAFB 0%, #F8FAFC 100%);
        }

        /* 底部安全区域 */
        .bottom-safe-area {
            height: 34px;
            background: linear-gradient(180deg, #FFFFFF 0%, #F8FAFC 100%);
        }

        /* 页面显示 */
        .page {
            height: 100%;
            display: block;
        }

        /* 底部导航 */
        .bottom-nav {
            position: absolute;
            bottom: 34px;
            left: 0;
            right: 0;
            height: 70px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(229, 231, 235, 0.8);
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding: 0 24px;
            box-shadow: 0 -2px 20px rgba(0, 0, 0, 0.05);
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 2px;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 8px 12px;
            border-radius: 12px;
            position: relative;
        }

        .nav-item.active {
            color: #6366F1;
            background: rgba(99, 102, 241, 0.1);
        }

        .nav-item:not(.active) {
            color: #6B7280;
        }

        .nav-item:not(.active):hover {
            color: #4B5563;
            background: rgba(107, 114, 128, 0.05);
        }

        .add-button {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #6366F1, #EC4899);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 20px rgba(99, 102, 241, 0.3);
            position: relative;
        }

        .add-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 25px rgba(99, 102, 241, 0.4);
        }

        .add-button::before {
            content: '';
            position: absolute;
            inset: -2px;
            background: linear-gradient(135deg, #6366F1, #EC4899);
            border-radius: 18px;
            z-index: -1;
            opacity: 0.3;
            filter: blur(8px);
        }

        /* 卡片样式 */
        .info-card {
            background: #FFFFFF;
            border-radius: 16px;
            padding: 14px;
            margin: 8px 12px;
            border: 1px solid #E5E7EB;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .info-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #6366F1, #EC4899, #10B981);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .info-card:hover::before {
            opacity: 1;
        }

        .info-card:hover {
            transform: translateY(-2px);
            border-color: #D1D5DB;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
        }

        /* 头像 */
        .avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: linear-gradient(135deg, #6366F1, #EC4899);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
            border: 2px solid #FFFFFF;
            box-shadow: 0 2px 8px rgba(99, 102, 241, 0.2);
        }

        .avatar-large {
            width: 72px;
            height: 72px;
            font-size: 20px;
            border: 3px solid #FFFFFF;
        }

        /* 标签样式 */
        .tag {
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        .tag-vip {
            background: linear-gradient(135deg, #F59E0B, #F97316);
            color: white;
        }

        .tag-category {
            background: rgba(99, 102, 241, 0.1);
            color: #6366F1;
            border: 1px solid rgba(99, 102, 241, 0.2);
        }

        .tag-level {
            background: rgba(16, 185, 129, 0.1);
            color: #059669;
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        /* 启动页动画 */
        .splash-logo {
            animation: pulse-slow 2s infinite;
        }

        /* 滚动条隐藏 */
        .safe-area::-webkit-scrollbar {
            display: none;
        }
    </style>
</head>

<body class="bg-gray-100 font-sf">
    <div class="pages-container">
        <!-- 启动页 -->
        <div class="mobile-container">
            <div class="screen">
                <!-- 状态栏 -->
                <div class="status-bar">
                    <span>9:41</span>
                    <span>启动页</span>
                    <div class="flex items-center gap-1">
                        <span>100%</span>
                        <svg width="24" height="12" viewBox="0 0 24 12" fill="currentColor">
                            <rect x="1" y="3" width="20" height="6" rx="2" fill="white" />
                            <rect x="2" y="4" width="18" height="4" rx="1" fill="currentColor" />
                            <rect x="22" y="5" width="1" height="2" rx="0.5" fill="currentColor" />
                        </svg>
                    </div>
                </div>

                <!-- 启动页内容 -->
                <div class="page">
                    <div
                        class="safe-area flex flex-col items-center justify-center bg-gradient-to-br from-primary via-secondary to-accent text-white relative overflow-hidden">
                        <!-- 背景装饰 -->
                        <div class="absolute inset-0">
                            <div
                                class="absolute top-20 left-10 w-32 h-32 bg-white bg-opacity-10 rounded-full blur-xl animate-pulse">
                            </div>
                            <div
                                class="absolute bottom-32 right-8 w-24 h-24 bg-white bg-opacity-15 rounded-full blur-lg animate-bounce">
                            </div>
                            <div
                                class="absolute top-1/2 left-1/4 w-16 h-16 bg-white bg-opacity-5 rounded-full blur-md animate-pulse">
                            </div>
                        </div>

                        <div class="splash-logo mb-6 relative z-10">
                            <div class="relative">
                                <svg width="100" height="100" viewBox="0 0 100 100" fill="none" class="drop-shadow-lg">
                                    <defs>
                                        <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                            <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:1" />
                                            <stop offset="100%" style="stop-color:#F8FAFC;stop-opacity:0.8" />
                                        </linearGradient>
                                    </defs>
                                    <circle cx="50" cy="50" r="45" fill="url(#logoGradient)" fill-opacity="0.2" />
                                    <circle cx="50" cy="50" r="30" fill="url(#logoGradient)" fill-opacity="0.4" />
                                    <rect x="35" y="42" width="30" height="16" rx="8" fill="white" />
                                    <circle cx="42" cy="50" r="3" fill="#6366F1" />
                                    <circle cx="50" cy="50" r="3" fill="#EC4899" />
                                    <circle cx="58" cy="50" r="3" fill="#10B981" />
                                </svg>
                                <div
                                    class="absolute inset-0 bg-white bg-opacity-20 rounded-full blur-2xl animate-pulse">
                                </div>
                            </div>
                        </div>

                        <div class="text-center mb-8 relative z-10">
                            <h1 class="text-2xl font-bold mb-2 tracking-wide">信息差</h1>
                            <p class="text-sm opacity-90 font-medium">发现价值信息 · 把握先机</p>
                        </div>

                        <div class="flex items-center gap-2 text-xs opacity-75 relative z-10">
                            <div class="w-2 h-2 bg-white rounded-full animate-bounce"></div>
                            <div class="w-2 h-2 bg-white rounded-full animate-bounce" style="animation-delay: 0.1s">
                            </div>
                            <div class="w-2 h-2 bg-white rounded-full animate-bounce" style="animation-delay: 0.2s">
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 底部安全区域 -->
                <div class="bottom-safe-area"></div>
            </div>
        </div>

        <!-- 首页 -->
        <div class="mobile-container">
            <div class="screen">
                <!-- 状态栏 -->
                <div class="status-bar">
                    <span>9:41</span>
                    <span>首页</span>
                    <div class="flex items-center gap-1">
                        <span>100%</span>
                        <svg width="24" height="12" viewBox="0 0 24 12" fill="currentColor">
                            <rect x="1" y="3" width="20" height="6" rx="2" fill="white" />
                            <rect x="2" y="4" width="18" height="4" rx="1" fill="currentColor" />
                            <rect x="22" y="5" width="1" height="2" rx="0.5" fill="currentColor" />
                        </svg>
                    </div>
                </div>

                <!-- 首页内容 -->
                <div class="page">
                    <div class="safe-area">
                        <!-- 顶部导航 -->
                        <div class="bg-white px-4 py-3 border-b border-border-light">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center gap-3">
                                    <div
                                        class="w-8 h-8 bg-gradient-to-br from-primary to-secondary rounded-xl flex items-center justify-center">
                                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h1 class="text-lg font-bold text-text-primary">信息差</h1>
                                        <p class="text-xs text-text-muted">发现价值信息</p>
                                    </div>
                                </div>
                                <div class="flex items-center gap-2">
                                    <button class="p-2 rounded-lg bg-bg-secondary">
                                        <svg class="w-5 h-5 text-text-secondary" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                        </svg>
                                    </button>
                                    <button class="p-2 rounded-lg bg-bg-secondary">
                                        <svg class="w-5 h-5 text-text-secondary" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 统计卡片 -->
                        <div class="px-4 py-3 bg-white border-b border-border-light">
                            <div class="grid grid-cols-4 gap-3">
                                <div class="text-center">
                                    <div class="text-lg font-bold text-primary">2.3K</div>
                                    <div class="text-xs text-text-muted">今日新增</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-lg font-bold text-secondary">156</div>
                                    <div class="text-xs text-text-muted">热门话题</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-lg font-bold text-accent">89K</div>
                                    <div class="text-xs text-text-muted">总用户</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-lg font-bold text-warning">24h</div>
                                    <div class="text-xs text-text-muted">实时更新</div>
                                </div>
                            </div>
                        </div>

                        <!-- 分类标签 -->
                        <div class="px-4 py-2 bg-white border-b border-border-light">
                            <div class="flex gap-2 overflow-x-auto">
                                <button
                                    class="px-3 py-1.5 bg-primary text-white rounded-full text-xs font-medium whitespace-nowrap">全部</button>
                                <button
                                    class="px-3 py-1.5 bg-bg-secondary text-text-secondary rounded-full text-xs font-medium whitespace-nowrap">科技</button>
                                <button
                                    class="px-3 py-1.5 bg-bg-secondary text-text-secondary rounded-full text-xs font-medium whitespace-nowrap">商业</button>
                                <button
                                    class="px-3 py-1.5 bg-bg-secondary text-text-secondary rounded-full text-xs font-medium whitespace-nowrap">投资</button>
                                <button
                                    class="px-3 py-1.5 bg-bg-secondary text-text-secondary rounded-full text-xs font-medium whitespace-nowrap">生活</button>
                                <button
                                    class="px-3 py-1.5 bg-bg-secondary text-text-secondary rounded-full text-xs font-medium whitespace-nowrap">其他</button>
                            </div>
                        </div>

                        <!-- 信息卡片列表 -->
                        <div class="pb-24">
                            <!-- 信息卡片1 -->
                            <div class="info-card" onclick="showPostDetail(1)">
                                <div class="flex items-start gap-3 mb-2">
                                    <div class="avatar">
                                        <span>张</span>
                                    </div>
                                    <div class="flex-1">
                                        <div class="flex items-center gap-2 mb-1">
                                            <span class="font-semibold text-text-primary text-sm">张三</span>
                                            <span class="tag tag-vip">VIP</span>
                                            <span class="tag tag-level">Lv.8</span>
                                        </div>
                                        <div class="flex items-center gap-2 text-xs text-text-muted">
                                            <span>2小时前</span>
                                            <span>·</span>
                                            <span>阅读 1.2K</span>
                                        </div>
                                    </div>
                                    <button class="p-1 rounded-lg hover:bg-bg-secondary">
                                        <svg class="w-4 h-4 text-text-muted" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M5 12h.01M12 12h.01M19 12h.01" />
                                        </svg>
                                    </button>
                                </div>

                                <h3 class="font-semibold text-text-primary text-sm mb-2 leading-relaxed">
                                    AI绘画工具Midjourney即将推出新功能，视频生成时代来临</h3>
                                <p class="text-text-secondary text-xs mb-3 leading-relaxed">
                                    据内部消息，Midjourney将在下周发布V6版本，新增视频生成功能，现在是入手会员的最佳时机。预计价格将上涨30%...</p>

                                <!-- 图片预览 -->
                                <div class="mb-3">
                                    <div class="grid grid-cols-3 gap-2">
                                        <div
                                            class="aspect-square bg-gradient-to-br from-primary-light to-primary rounded-lg flex items-center justify-center">
                                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                            </svg>
                                        </div>
                                        <div
                                            class="aspect-square bg-gradient-to-br from-secondary-light to-secondary rounded-lg flex items-center justify-center">
                                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                            </svg>
                                        </div>
                                        <div
                                            class="aspect-square bg-gradient-to-br from-accent-light to-accent rounded-lg flex items-center justify-center">
                                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                            </svg>
                                        </div>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-4">
                                        <button
                                            class="flex items-center gap-1 text-text-muted hover:text-danger transition-colors">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                            </svg>
                                            <span class="text-xs">128</span>
                                        </button>
                                        <button
                                            class="flex items-center gap-1 text-text-muted hover:text-primary transition-colors">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                            </svg>
                                            <span class="text-xs">32</span>
                                        </button>
                                        <button
                                            class="flex items-center gap-1 text-text-muted hover:text-warning transition-colors">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                                            </svg>
                                            <span class="text-xs">收藏</span>
                                        </button>
                                    </div>
                                    <span class="tag tag-category">科技</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 底部导航 -->
                    <div class="bottom-nav">
                        <div class="nav-item active">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z" />
                            </svg>
                            <span class="text-xs">首页</span>
                        </div>

                        <div class="add-button">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                        </div>

                        <div class="nav-item">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path
                                    d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
                            </svg>
                            <span class="text-xs">我的</span>
                        </div>
                    </div>
                    <!-- 底部安全区域 -->
                    <div class="bottom-safe-area"></div>
                </div>
            </div>

            <!-- 帖子详情页 -->
            <div class="mobile-container">
                <div class="screen">
                    <!-- 状态栏 -->
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>帖子详情</span>
                        <div class="flex items-center gap-1">
                            <span>100%</span>
                            <svg width="24" height="12" viewBox="0 0 24 12" fill="currentColor">
                                <rect x="1" y="3" width="20" height="6" rx="2" fill="white" />
                                <rect x="2" y="4" width="18" height="4" rx="1" fill="currentColor" />
                                <rect x="22" y="5" width="1" height="2" rx="0.5" fill="currentColor" />
                            </svg>
                        </div>
                    </div>

                    <!-- 帖子详情内容 -->
                    <div class="page">
                        <div class="safe-area">
                            <!-- 顶部导航 -->
                            <div class="bg-white px-4 py-3 border-b border-border-light">
                                <div class="flex items-center justify-between">
                                    <button onclick="showPage('home')"
                                        class="p-2 -ml-2 rounded-lg hover:bg-bg-secondary">
                                        <svg class="w-5 h-5 text-text-secondary" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M15 19l-7-7 7-7" />
                                        </svg>
                                    </button>
                                    <h1 class="text-lg font-semibold text-text-primary">详情</h1>
                                    <button class="p-2 -mr-2 rounded-lg hover:bg-bg-secondary">
                                        <svg class="w-5 h-5 text-text-secondary" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            <!-- 帖子内容 -->
                            <div class="p-4">
                                <!-- 作者信息 -->
                                <div class="flex items-start gap-3 mb-4">
                                    <div class="avatar">
                                        <span>张</span>
                                    </div>
                                    <div class="flex-1">
                                        <div class="flex items-center gap-2 mb-1">
                                            <span class="font-semibold text-text-primary">张三</span>
                                            <span class="tag tag-vip">VIP</span>
                                            <span class="tag tag-level">Lv.8</span>
                                        </div>
                                        <div class="flex items-center gap-2 text-xs text-text-muted">
                                            <span>2小时前发布</span>
                                            <span>·</span>
                                            <span>已有 1.2K 人阅读</span>
                                        </div>
                                    </div>
                                    <button class="px-3 py-1.5 bg-primary text-white rounded-lg text-xs font-medium">
                                        关注
                                    </button>
                                </div>

                                <!-- 标题 -->
                                <h1 class="text-lg font-bold text-text-primary mb-3 leading-relaxed">
                                    AI绘画工具Midjourney即将推出新功能，视频生成时代来临
                                </h1>

                                <!-- 标签 -->
                                <div class="flex items-center gap-2 mb-4">
                                    <span class="tag tag-category">科技</span>
                                    <span class="tag"
                                        style="background: rgba(239, 68, 68, 0.1); color: #EF4444; border: 1px solid rgba(239, 68, 68, 0.2);">热门</span>
                                    <span class="tag"
                                        style="background: rgba(245, 158, 11, 0.1); color: #F59E0B; border: 1px solid rgba(245, 158, 11, 0.2);">独家</span>
                                </div>

                                <!-- 内容 -->
                                <div class="prose prose-sm max-w-none text-text-secondary leading-relaxed mb-6">
                                    <p class="mb-4">据可靠内部消息，AI绘画工具Midjourney将在下周正式发布V6版本，这次更新将带来革命性的视频生成功能。</p>

                                    <p class="mb-4">新功能亮点包括：</p>
                                    <ul class="list-disc list-inside mb-4 space-y-1">
                                        <li>支持最长60秒的高质量视频生成</li>
                                        <li>提供多种视频风格和转场效果</li>
                                        <li>集成音频同步功能</li>
                                        <li>支持批量处理和自动化工作流</li>
                                    </ul>

                                    <p class="mb-4">根据内测用户反馈，新版本的视频质量已经达到商业级别，预计将对短视频创作行业产生重大影响。</p>

                                    <div class="bg-bg-secondary rounded-lg p-3 mb-4">
                                        <p class="text-xs text-text-muted mb-1">💡 投资建议</p>
                                        <p class="text-sm">
                                            现在是入手Midjourney会员的最佳时机，预计价格将在新功能发布后上涨30%。建议提前购买年度会员锁定价格。
                                        </p>
                                    </div>
                                </div>

                                <!-- 图片展示 -->
                                <div class="grid grid-cols-2 gap-3 mb-6">
                                    <div
                                        class="aspect-video bg-gradient-to-br from-primary-light to-primary rounded-lg flex items-center justify-center">
                                        <div class="text-center text-white">
                                            <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                            </svg>
                                            <p class="text-xs">演示视频</p>
                                        </div>
                                    </div>
                                    <div
                                        class="aspect-video bg-gradient-to-br from-secondary-light to-secondary rounded-lg flex items-center justify-center">
                                        <div class="text-center text-white">
                                            <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                            </svg>
                                            <p class="text-xs">效果对比</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- 互动区域 -->
                                <div class="flex items-center justify-between py-3 border-t border-border-light">
                                    <div class="flex items-center gap-6">
                                        <button
                                            class="flex items-center gap-2 text-text-muted hover:text-danger transition-colors">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                            </svg>
                                            <span class="text-sm font-medium">128</span>
                                        </button>
                                        <button
                                            class="flex items-center gap-2 text-text-muted hover:text-primary transition-colors">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                            </svg>
                                            <span class="text-sm font-medium">32</span>
                                        </button>
                                        <button
                                            class="flex items-center gap-2 text-text-muted hover:text-warning transition-colors">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                                            </svg>
                                            <span class="text-sm font-medium">收藏</span>
                                        </button>
                                    </div>
                                </div>

                                <!-- 评论区 -->
                                <div class="mt-6">
                                    <h3 class="text-sm font-semibold text-text-primary mb-4">评论 (32)</h3>

                                    <!-- 评论输入 -->
                                    <div class="flex gap-3 mb-4">
                                        <div class="avatar" style="width: 32px; height: 32px; font-size: 12px;">
                                            <span>我</span>
                                        </div>
                                        <div class="flex-1">
                                            <input type="text" placeholder="写下你的想法..."
                                                class="w-full px-3 py-2 bg-bg-secondary rounded-lg border-none outline-none text-sm placeholder-text-muted">
                                        </div>
                                    </div>

                                    <!-- 评论列表 -->
                                    <div class="space-y-4">
                                        <div class="flex gap-3">
                                            <div class="avatar" style="width: 32px; height: 32px; font-size: 12px;">
                                                <span>李</span>
                                            </div>
                                            <div class="flex-1">
                                                <div class="flex items-center gap-2 mb-1">
                                                    <span class="text-sm font-medium text-text-primary">李四</span>
                                                    <span class="text-xs text-text-muted">1小时前</span>
                                                </div>
                                                <p class="text-sm text-text-secondary mb-2">
                                                    确实是个好消息，我已经在用Midjourney了，期待视频功能！</p>
                                                <div class="flex items-center gap-4">
                                                    <button
                                                        class="flex items-center gap-1 text-text-muted hover:text-danger transition-colors">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                            viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                stroke-width="2"
                                                                d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                                        </svg>
                                                        <span class="text-xs">5</span>
                                                    </button>
                                                    <button
                                                        class="text-xs text-text-muted hover:text-primary transition-colors">回复</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 底部安全区域 -->
                    <div class="bottom-safe-area"></div>
                </div>
            </div>

            <!-- 分享页面 -->
            <div class="mobile-container">
                <div class="screen">
                    <!-- 状态栏 -->
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>分享信息</span>
                        <div class="flex items-center gap-1">
                            <span>100%</span>
                            <svg width="24" height="12" viewBox="0 0 24 12" fill="currentColor">
                                <rect x="1" y="3" width="20" height="6" rx="2" fill="white" />
                                <rect x="2" y="4" width="18" height="4" rx="1" fill="currentColor" />
                                <rect x="22" y="5" width="1" height="2" rx="0.5" fill="currentColor" />
                            </svg>
                        </div>
                    </div>

                    <!-- 分享页面内容 -->
                    <div class="page">
                        <div class="safe-area">
                            <!-- 顶部导航 -->
                            <div class="bg-white px-4 py-3 border-b border-border-light">
                                <div class="flex items-center justify-between">
                                    <button class="p-2 -ml-2 rounded-lg hover:bg-bg-secondary">
                                        <svg class="w-5 h-5 text-text-secondary" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M15 19l-7-7 7-7" />
                                        </svg>
                                    </button>
                                    <h1 class="text-lg font-semibold text-text-primary">分享信息差</h1>
                                    <button class="text-primary font-semibold">发布</button>
                                </div>
                            </div>

                            <!-- 分享表单 -->
                            <div class="p-4 space-y-4">
                                <!-- 标题输入 -->
                                <div class="bg-white rounded-xl p-4">
                                    <label class="block text-sm font-medium text-text-primary mb-2">标题</label>
                                    <input type="text" placeholder="输入信息差标题..."
                                        class="w-full border-none outline-none text-lg font-semibold placeholder-text-muted">
                                </div>

                                <!-- 内容输入 -->
                                <div class="bg-white rounded-xl p-4">
                                    <label class="block text-sm font-medium text-text-primary mb-2">内容</label>
                                    <textarea placeholder="分享你的信息差内容..." rows="8"
                                        class="w-full border-none outline-none resize-none placeholder-text-muted"></textarea>
                                </div>

                                <!-- 分类选择 -->
                                <div class="bg-white rounded-xl p-4">
                                    <label class="block text-sm font-medium text-text-primary mb-3">选择分类</label>
                                    <div class="flex flex-wrap gap-2">
                                        <button
                                            class="px-3 py-1.5 bg-primary text-white rounded-full text-xs font-medium">科技</button>
                                        <button
                                            class="px-3 py-1.5 bg-bg-secondary text-text-secondary rounded-full text-xs font-medium">商业</button>
                                        <button
                                            class="px-3 py-1.5 bg-bg-secondary text-text-secondary rounded-full text-xs font-medium">投资</button>
                                        <button
                                            class="px-3 py-1.5 bg-bg-secondary text-text-secondary rounded-full text-xs font-medium">生活</button>
                                        <button
                                            class="px-3 py-1.5 bg-bg-secondary text-text-secondary rounded-full text-xs font-medium">其他</button>
                                    </div>
                                </div>

                                <!-- 图片上传 -->
                                <div class="bg-white rounded-xl p-4">
                                    <label class="block text-sm font-medium text-text-primary mb-3">添加图片（可选）</label>
                                    <div class="border-2 border-dashed border-border-light rounded-lg p-8 text-center">
                                        <svg class="w-12 h-12 text-text-muted mx-auto mb-2" fill="none"
                                            stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                        </svg>
                                        <p class="text-text-muted">点击上传图片</p>
                                    </div>
                                </div>

                                <!-- 发布设置 -->
                                <div class="bg-white rounded-xl p-4">
                                    <div class="flex items-center justify-between mb-3">
                                        <span class="font-medium text-text-primary">设为付费内容</span>
                                        <div class="relative">
                                            <input type="checkbox" class="sr-only">
                                            <div class="w-10 h-6 bg-border-medium rounded-full"></div>
                                            <div class="absolute w-4 h-4 bg-white rounded-full left-1 top-1 transition">
                                            </div>
                                        </div>
                                    </div>
                                    <p class="text-sm text-text-muted">开启后，用户需要付费才能查看完整内容</p>
                                </div>
                            </div>
                        </div>
                        <!-- 底部安全区域 -->
                        <div class="bottom-safe-area"></div>
                    </div>
                </div>
            </div>

            <!-- 我的页面 -->
            <div class="mobile-container">
                <div class="screen">
                    <!-- 状态栏 -->
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>我的</span>
                        <div class="flex items-center gap-1">
                            <span>100%</span>
                            <svg width="24" height="12" viewBox="0 0 24 12" fill="currentColor">
                                <rect x="1" y="3" width="20" height="6" rx="2" fill="white" />
                                <rect x="2" y="4" width="18" height="4" rx="1" fill="currentColor" />
                                <rect x="22" y="5" width="1" height="2" rx="0.5" fill="currentColor" />
                            </svg>
                        </div>
                    </div>

                    <!-- 我的页面内容 -->
                    <div class="page">
                        <div class="safe-area">
                            <!-- 个人信息卡片 -->
                            <div class="bg-gradient-to-br from-primary to-secondary p-6 text-white">
                                <div class="flex items-center gap-4 mb-6">
                                    <div class="avatar avatar-large">
                                        <span>我</span>
                                    </div>
                                    <div class="flex-1">
                                        <h2 class="text-xl font-bold mb-1">我的昵称</h2>
                                        <p class="opacity-80">ID: 123456789</p>
                                        <div class="flex items-center gap-2 mt-2">
                                            <span class="px-2 py-1 bg-white bg-opacity-20 rounded text-sm">VIP会员</span>
                                            <span class="px-2 py-1 bg-white bg-opacity-20 rounded text-sm">Lv.5</span>
                                        </div>
                                    </div>
                                    <button class="text-white">
                                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9 5l7 7-7 7" />
                                        </svg>
                                    </button>
                                </div>

                                <!-- 余额信息 -->
                                <div class="bg-white bg-opacity-20 rounded-xl p-4">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="opacity-80 text-sm">账户余额</p>
                                            <p class="text-2xl font-bold">¥1,234.56</p>
                                        </div>
                                        <button class="bg-white text-primary px-4 py-2 rounded-lg font-semibold">
                                            充值
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- 统计数据 -->
                            <div class="bg-white mx-4 -mt-6 rounded-xl p-4 relative z-10">
                                <div class="grid grid-cols-3 gap-4 text-center">
                                    <div>
                                        <p class="text-2xl font-bold text-text-primary">23</p>
                                        <p class="text-sm text-text-muted">发布</p>
                                    </div>
                                    <div>
                                        <p class="text-2xl font-bold text-text-primary">156</p>
                                        <p class="text-sm text-text-muted">获赞</p>
                                    </div>
                                    <div>
                                        <p class="text-2xl font-bold text-text-primary">89</p>
                                        <p class="text-sm text-text-muted">收藏</p>
                                    </div>
                                </div>
                            </div>

                            <!-- 功能菜单 -->
                            <div class="p-4 space-y-4 mt-4">
                                <!-- 我的内容 -->
                                <div class="bg-white rounded-xl overflow-hidden">
                                    <div class="p-4 border-b border-border-light">
                                        <h3 class="font-semibold text-text-primary">我的内容</h3>
                                    </div>
                                    <div class="space-y-0">
                                        <button
                                            class="w-full flex items-center justify-between p-4 hover:bg-bg-secondary">
                                            <div class="flex items-center gap-3">
                                                <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor"
                                                    viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                                </svg>
                                                <span class="text-text-primary">我的发布</span>
                                            </div>
                                            <svg class="w-5 h-5 text-text-muted" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 5l7 7-7 7" />
                                            </svg>
                                        </button>
                                        <button
                                            class="w-full flex items-center justify-between p-4 hover:bg-bg-secondary">
                                            <div class="flex items-center gap-3">
                                                <svg class="w-5 h-5 text-warning" fill="none" stroke="currentColor"
                                                    viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                                </svg>
                                                <span class="text-text-primary">我的收藏</span>
                                            </div>
                                            <svg class="w-5 h-5 text-text-muted" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 5l7 7-7 7" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>

                                <!-- 设置 -->
                                <div class="bg-white rounded-xl overflow-hidden">
                                    <div class="p-4 border-b border-border-light">
                                        <h3 class="font-semibold text-text-primary">设置</h3>
                                    </div>
                                    <div class="space-y-0">
                                        <button
                                            class="w-full flex items-center justify-between p-4 hover:bg-bg-secondary">
                                            <div class="flex items-center gap-3">
                                                <svg class="w-5 h-5 text-text-secondary" fill="none"
                                                    stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                                </svg>
                                                <span class="text-text-primary">个人资料</span>
                                            </div>
                                            <svg class="w-5 h-5 text-text-muted" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 5l7 7-7 7" />
                                            </svg>
                                        </button>
                                        <button
                                            class="w-full flex items-center justify-between p-4 hover:bg-bg-secondary">
                                            <div class="flex items-center gap-3">
                                                <svg class="w-5 h-5 text-text-secondary" fill="none"
                                                    stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                </svg>
                                                <span class="text-text-primary">通用设置</span>
                                            </div>
                                            <svg class="w-5 h-5 text-text-muted" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 5l7 7-7 7" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 底部安全区域 -->
                        <div class="bottom-safe-area"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>
<div class="flex items-center justify-between">
    <div class="flex items-center gap-4">
        <div class="flex items-center gap-1">
            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
            </svg>
            <span class="text-sm text-gray-500">256</span>
        </div>
        <div class="flex items-center gap-1">
            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
            <span class="text-sm text-gray-500">89</span>
        </div>
    </div>
    <span class="px-2 py-1 bg-warning text-white text-xs rounded">生活</span>
</div>
</div>

<!-- 信息卡片3 -->
<div class="info-card">
    <div class="flex items-start gap-3 mb-3">
        <div class="avatar">
            <span>王</span>
        </div>
        <div class="flex-1">
            <div class="flex items-center gap-2 mb-1">
                <span class="font-semibold text-gray-900">王五</span>
                <span class="px-2 py-1 bg-warning text-white text-xs rounded">VIP</span>
            </div>
            <span class="text-sm text-gray-500">1天前</span>
        </div>
        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h.01M12 12h.01M19 12h.01" />
        </svg>
    </div>
    <h3 class="font-semibold text-gray-900 mb-2">新兴投资赛道分析报告</h3>
    <p class="text-gray-600 text-sm mb-3">基于最新市场数据，分析了三个具有高增长潜力的投资方向，建议关注...</p>
    <div class="flex items-center justify-between">
        <div class="flex items-center gap-4">
            <div class="flex items-center gap-1">
                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
                <span class="text-sm text-gray-500">512</span>
            </div>
            <div class="flex items-center gap-1">
                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
                <span class="text-sm text-gray-500">156</span>
            </div>
        </div>
        <span class="px-2 py-1 bg-secondary text-white text-xs rounded">投资</span>
    </div>
</div>
</div>
</div>
</div>

<!-- 分享页面 -->
<div id="share" class="page">
    <div class="safe-area bg-gray-50">
        <!-- 顶部导航 -->
        <div class="bg-white px-4 py-3 border-b border-gray-100">
            <div class="flex items-center justify-between">
                <button onclick="showPage('home')" class="text-primary">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                    </svg>
                </button>
                <h1 class="text-lg font-semibold text-gray-900">分享信息差</h1>
                <button class="text-primary font-semibold">发布</button>
            </div>
        </div>

        <!-- 分享表单 -->
        <div class="p-4 space-y-4">
            <!-- 标题输入 -->
            <div class="bg-white rounded-xl p-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">标题</label>
                <input type="text" placeholder="输入信息差标题..."
                    class="w-full border-none outline-none text-lg font-semibold placeholder-gray-400">
            </div>

            <!-- 内容输入 -->
            <div class="bg-white rounded-xl p-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">内容</label>
                <textarea placeholder="分享你的信息差内容..." rows="8"
                    class="w-full border-none outline-none resize-none placeholder-gray-400"></textarea>
            </div>

            <!-- 分类选择 -->
            <div class="bg-white rounded-xl p-4">
                <label class="block text-sm font-medium text-gray-700 mb-3">选择分类</label>
                <div class="flex flex-wrap gap-2">
                    <button class="px-4 py-2 bg-primary text-white rounded-full text-sm">科技</button>
                    <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm">商业</button>
                    <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm">投资</button>
                    <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm">生活</button>
                    <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm">其他</button>
                </div>
            </div>

            <!-- 图片上传 -->
            <div class="bg-white rounded-xl p-4">
                <label class="block text-sm font-medium text-gray-700 mb-3">添加图片（可选）</label>
                <div class="border-2 border-dashed border-gray-200 rounded-lg p-8 text-center">
                    <svg class="w-12 h-12 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor"
                        viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    <p class="text-gray-500">点击上传图片</p>
                </div>
            </div>

            <!-- 发布设置 -->
            <div class="bg-white rounded-xl p-4">
                <div class="flex items-center justify-between mb-3">
                    <span class="font-medium text-gray-700">设为付费内容</span>
                    <div class="relative">
                        <input type="checkbox" class="sr-only">
                        <div class="w-10 h-6 bg-gray-200 rounded-full shadow-inner"></div>
                        <div class="absolute w-4 h-4 bg-white rounded-full shadow left-1 top-1 transition">
                        </div>
                    </div>
                </div>
                <p class="text-sm text-gray-500">开启后，用户需要付费才能查看完整内容</p>
            </div>
        </div>
    </div>
</div>

<!-- 我的页面 -->
<div id="profile" class="page">
    <div class="safe-area bg-gray-50">
        <!-- 个人信息卡片 -->
        <div class="bg-gradient-to-br from-primary to-secondary p-6 text-white">
            <div class="flex items-center gap-4 mb-6">
                <div class="avatar avatar-large">
                    <span>我</span>
                </div>
                <div class="flex-1">
                    <h2 class="text-xl font-bold mb-1">我的昵称</h2>
                    <p class="opacity-80">ID: 123456789</p>
                    <div class="flex items-center gap-2 mt-2">
                        <span class="px-2 py-1 bg-white bg-opacity-20 rounded text-sm">VIP会员</span>
                        <span class="px-2 py-1 bg-white bg-opacity-20 rounded text-sm">Lv.5</span>
                    </div>
                </div>
                <button class="text-white">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </button>
            </div>

            <!-- 余额信息 -->
            <div class="bg-white bg-opacity-20 rounded-xl p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="opacity-80 text-sm">账户余额</p>
                        <p class="text-2xl font-bold">¥1,234.56</p>
                    </div>
                    <button class="bg-white text-primary px-4 py-2 rounded-lg font-semibold">
                        充值
                    </button>
                </div>
            </div>
        </div>

        <!-- 统计数据 -->
        <div class="bg-white mx-4 -mt-6 rounded-xl p-4 relative z-10">
            <div class="grid grid-cols-3 gap-4 text-center">
                <div>
                    <p class="text-2xl font-bold text-gray-900">23</p>
                    <p class="text-sm text-gray-500">发布</p>
                </div>
                <div>
                    <p class="text-2xl font-bold text-gray-900">156</p>
                    <p class="text-sm text-gray-500">获赞</p>
                </div>
                <div>
                    <p class="text-2xl font-bold text-gray-900">89</p>
                    <p class="text-sm text-gray-500">收藏</p>
                </div>
            </div>
        </div>

        <!-- 功能菜单 -->
        <div class="p-4 space-y-4 mt-4">
            <!-- 我的内容 -->
            <div class="bg-white rounded-xl overflow-hidden">
                <div class="p-4 border-b border-gray-100">
                    <h3 class="font-semibold text-gray-900">我的内容</h3>
                </div>
                <div class="space-y-0">
                    <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50">
                        <div class="flex items-center gap-3">
                            <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            <span class="text-gray-900">我的发布</span>
                        </div>
                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </button>
                    <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50">
                        <div class="flex items-center gap-3">
                            <svg class="w-5 h-5 text-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                            </svg>
                            <span class="text-gray-900">我的收藏</span>
                        </div>
                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </button>
                    <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50">
                        <div class="flex items-center gap-3">
                            <svg class="w-5 h-5 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                            </svg>
                            <span class="text-gray-900">收益明细</span>
                        </div>
                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </button>
                </div>
            </div>

            <!-- 设置 -->
            <div class="bg-white rounded-xl overflow-hidden">
                <div class="p-4 border-b border-gray-100">
                    <h3 class="font-semibold text-gray-900">设置</h3>
                </div>
                <div class="space-y-0">
                    <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50">
                        <div class="flex items-center gap-3">
                            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            <span class="text-gray-900">个人资料</span>
                        </div>
                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </button>
                    <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50">
                        <div class="flex items-center gap-3">
                            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            <span class="text-gray-900">通用设置</span>
                        </div>
                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </button>
                    <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50">
                        <div class="flex items-center gap-3">
                            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span class="text-gray-900">帮助与反馈</span>
                        </div>
                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 底部导航 -->
<div class="bottom-nav">
    <div class="nav-item active" onclick="showPage('home')">
        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
            <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z" />
        </svg>
        <span class="text-xs">首页</span>
    </div>

    <div class="add-button" onclick="showPage('share')">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
    </div>

    <div class="nav-item" onclick="showPage('profile')">
        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
            <path
                d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
        </svg>
        <span class="text-xs">我的</span>
    </div>
</div>

<!-- 底部安全区域 -->
<div class="bottom-safe-area"></div>
</div>
</div>

<script>
    // 页面切换功能
    function showPage(pageId) {
        // 隐藏所有页面
        document.querySelectorAll('.page').forEach(page => {
            page.classList.remove('active');
        });

        // 显示指定页面
        document.getElementById(pageId).classList.add('active');

        // 更新底部导航状态
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });

        if (pageId === 'home') {
            document.querySelector('.nav-item:first-child').classList.add('active');
        } else if (pageId === 'profile') {
            document.querySelector('.nav-item:last-child').classList.add('active');
        }
    }

    // 显示帖子详情
    function showPostDetail(postId) {
        showPage('post-detail');
    }

    // 启动页自动跳转
    setTimeout(() => {
        showPage('home');
    }, 3000);

    // 分类标签切换
    document.addEventListener('click', function (e) {
        if (e.target.matches('.px-4.py-2') || e.target.closest('.px-4.py-2')) {
            const button = e.target.matches('.px-4.py-2') ? e.target : e.target.closest('.px-4.py-2');
            const container = button.closest('.flex');
            if (container) {
                const buttons = container.querySelectorAll('button');
                buttons.forEach(btn => {
                    btn.classList.remove('bg-primary', 'text-white');
                    btn.classList.add('bg-gray-100', 'text-gray-600');
                });
                button.classList.remove('bg-gray-100', 'text-gray-600');
                button.classList.add('bg-primary', 'text-white');
            }
        }
    });

    // 添加一些交互效果
    document.addEventListener('DOMContentLoaded', function () {
        // 卡片点击效果
        document.querySelectorAll('.info-card').forEach(card => {
            card.addEventListener('click', function () {
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });
        });

        // 按钮点击效果
        document.querySelectorAll('button').forEach(button => {
            button.addEventListener('click', function () {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 100);
            });
        });
    });
</script>
</body>

</html>