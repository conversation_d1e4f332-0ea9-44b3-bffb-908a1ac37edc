<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI自动记账APP原型设计</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
        }
        body {
            background-color: #f5f7fa;
            padding: 20px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-weight: 600;
        }
        .prototype-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 25px;
        }
        .app-screen {
            background-color: white;
            border: 16px solid #222;
            border-radius: 40px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            transition: transform 0.3s ease;
        }
        .app-screen:hover {
            transform: translateY(-5px);
        }
        .screen-header {
            height: 60px;
            background-color: #4A90E2;
            color: white;
            padding: 0 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
        }
        .screen-header::before {
            content: '';
            position: absolute;
            top: -16px;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 24px;
            background-color: #222;
            border-bottom-left-radius: 12px;
            border-bottom-right-radius: 12px;
        }
        .screen-title {
            font-size: 18px;
            font-weight: 500;
        }
        .screen-content {
            padding: 15px;
            min-height: 500px;
        }
        .screen-footer {
            height: 60px;
            background-color: white;
            border-top: 1px solid #eee;
            display: flex;
            justify-content: space-around;
            align-items: center;
            position: absolute;
            bottom: 0;
            width: 100%;
        }
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #999;
            font-size: 12px;
        }
        .nav-item.active {
            color: #4A90E2;
        }
        .nav-icon {
            font-size: 22px;
            margin-bottom: 4px;
        }
        .screen-description {
            padding: 15px;
            background-color: #f8f9fa;
            border-top: 1px solid #eee;
        }
        .screen-description h3 {
            color: #333;
            margin-bottom: 5px;
            font-size: 16px;
        }
        .screen-description p {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }
        /* 首页样式 */
        .balance-card {
            background: linear-gradient(135deg, #4A90E2, #5C6BC0);
            border-radius: 15px;
            padding: 20px;
            color: white;
            margin-bottom: 20px;
        }
        .balance-title {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 5px;
        }
        .balance-amount {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        .balance-stats {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-value {
            font-weight: 500;
        }
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-bottom: 25px;
        }
        .action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .action-icon {
            width: 50px;
            height: 50px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
            font-size: 22px;
            color: white;
        }
        .action-income {
            background-color: #4CAF50;
        }
        .action-expense {
            background-color: #F44336;
        }
        .action-transfer {
            background-color: #FFC107;
        }
        .action-scan {
            background-color: #9C27B0;
        }
        .action-name {
            font-size: 12px;
            color: #333;
        }
        .recent-transactions {
        }
        .section-title {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .see-all {
            font-size: 12px;
            color: #4A90E2;
        }
        .transaction-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #eee;
        }
        .transaction-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 18px;
        }
        .icon-food {
            background-color: #FFF3E0;
            color: #FF9800;
        }
        .icon-transport {
            background-color: #E3F2FD;
            color: #2196F3;
        }
        .icon-shopping {
            background-color: #F3E5F5;
            color: #9C27B0;
        }
        .transaction-details {
            flex: 1;
        }
        .transaction-title {
            font-size: 15px;
            color: #333;
            margin-bottom: 2px;
        }
        .transaction-time {
            font-size: 12px;
            color: #999;
        }
        .transaction-amount {
            font-size: 15px;
            font-weight: 500;
        }
        .amount-expense {
            color: #F44336;
        }
        .amount-income {
            color: #4CAF50;
        }
        /* 记账页面样式 */
        .record-form {
            padding: 10px 0;
        }
        .amount-input-container {
            text-align: center;
            margin: 30px 0;
        }
        .amount-label {
            font-size: 16px;
            color: #666;
            margin-bottom: 10px;
            display: block;
        }
        .amount-input {
            font-size: 40px;
            border: none;
            text-align: center;
            width: 100%;
            color: #333;
            font-weight: 600;
            background: transparent;
        }
        .amount-input:focus {
            outline: none;
        }
        .category-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-bottom: 30px;
        }
        .category-item {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .category-icon {
            width: 50px;
            height: 50px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
            font-size: 20px;
            background-color: #f5f5f5;
            color: #666;
        }
        .category-item.active .category-icon {
            background-color: #4A90E2;
            color: white;
        }
        .category-name {
            font-size: 12px;
            color: #333;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-label {
            display: block;
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
        }
        .form-input {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 10px;
            font-size: 15px;
        }
        .form-input:focus {
            outline: none;
            border-color: #4A90E2;
        }
        .save-btn {
            width: 100%;
            padding: 15px;
            background-color: #4A90E2;
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 500;
            margin-top: 20px;
        }
        /* 报表页面样式 */
        .chart-container {
            height: 200px;
            background-color: #f9f9f9;
            border-radius: 15px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
        }
        .category-stats {
            margin-top: 20px;
        }
        .stat-bar-container {
            margin-bottom: 15px;
        }
        .stat-bar-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 14px;
        }
        .stat-bar-label {
            color: #333;
        }
        .stat-bar-value {
            font-weight: 500;
        }
        .stat-bar {
            height: 8px;
            background-color: #eee;
            border-radius: 4px;
            overflow: hidden;
        }
        .stat-bar-fill {
            height: 100%;
            background-color: #4A90E2;
        }
        /* AI分析页面样式 */
        .ai-card {
            background-color: #E3F2FD;
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .ai-suggestion {
            background-color: white;
            border-left: 4px solid #4A90E2;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        .ai-suggestion-title {
            font-weight: 500;
            margin-bottom: 5px;
            color: #333;
        }
        .ai-suggestion-content {
            font-size: 14px;
            color: #666;
        }
        /* 登录页面样式 */
        .login-container {
            padding: 40px 20px;
        }
        .app-logo {
            width: 80px;
            height: 80px;
            background-color: #4A90E2;
            border-radius: 20px;
            margin: 0 auto 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 36px;
        }
        .login-form {
            margin-top: 30px;
        }
        .login-btn {
            width: 100%;
            padding: 15px;
            background-color: #4A90E2;
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 500;
            margin-top: 20px;
        }
        .login-footer {
            margin-top: 30px;
            text-align: center;
            font-size: 14px;
            color: #666;
        }
        .login-footer a {
            color: #4A90E2;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <h1>AI自动记账APP原型设计</h1>
    <div class="prototype-container">
        <!-- 登录界面 -->
        <div class="app-screen">
            <div class="screen-header">
                <div class="screen-title"></div>
            </div>
            <div class="screen-content login-container">
                <div class="app-logo"><i class="fas fa-wallet"></i></div>
                <h2 style="text-align: center; margin-bottom: 30px;">智能记账助手</h2>
                <div class="login-form">
                    <div class="form-group">
                        <input type="text" class="form-input" placeholder="手机号/邮箱">
                    </div>
                    <div class="form-group">
                        <input type="password" class="form-input" placeholder="密码">
                    </div>
                    <button class="login-btn">登录</button>
                </div>
                <div class="login-footer">
                    还没有账号？<a href="#">立即注册</a>
                </div>
            </div>
            <div class="screen-description">
                <h3>登录界面</h3>
                <p>用户登录/注册入口，支持手机号和邮箱登录，提供找回密码功能。</p>
            </div>
        </div>

        <!-- 首页仪表盘 -->
        <div class="app-screen">
            <div class="screen-header">
                <div class="screen-title">首页</div>
                <i class="fas fa-bell"></i>
            </div>
            <div class="screen-content">
                <div class="balance-card">
                    <div class="balance-title">本月余额</div>
                    <div class="balance-amount">¥12,560.80</div>
                    <div class="balance-stats">
                        <div class="stat-item">
                            <div class="stat-value">¥8,250.00</div>
                            <div class="stat-label">收入</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">¥5,320.20</div>
                            <div class="stat-label">支出</div>
                        </div>
                    </div>
                </div>
                <div class="quick-actions">
                    <div class="action-btn">
                        <div class="action-icon action-income"><i class="fas fa-plus"></i></div>
                        <div class="action-name">收入</div>
                    </div>
                    <div class="action-btn">
                        <div class="action-icon action-expense"><i class="fas fa-minus"></i></div>
                        <div class="action-name">支出</div>
                    </div>
                    <div class="action-btn">
                        <div class="action-icon action-transfer"><i class="fas fa-exchange-alt"></i></div>
                        <div class="action-name">转账</div>
                    </div>
                    <div class="action-btn">
                        <div class="action-icon action-scan"><i class="fas fa-qrcode"></i></div>
                        <div class="action-name">扫码</div>
                    </div>
                </div>
                <div class="recent-transactions">
                    <div class="section-title">
                        <span>最近交易</span>
                        <span class="see-all">全部</span>
                    </div>
                    <div class="transaction-item">
                        <div class="transaction-icon icon-food"><i class="fas fa-utensils"></i></div>
                        <div class="transaction-details">
                            <div class="transaction-title">午餐</div>
                            <div class="transaction-time">今天 12:30</div>
                        </div>
                        <div class="transaction-amount amount-expense">-¥45.00</div>
                    </div>
                    <div class="transaction-item">
                        <div class="transaction-icon icon-transport"><i class="fas fa-bus"></i></div>
                        <div class="transaction-details">
                            <div class="transaction-title">公交地铁</div>
                            <div class="transaction-time">今天 08:45</div>
                        </div>
                        <div class="transaction-amount amount-expense">-¥8.00</div>
                    </div>
                    <div class="transaction-item">
                        <div class="transaction-icon icon-shopping"><i class="fas fa-shopping-bag"></i></div>
                        <div class="transaction-details">
                            <div class="transaction-title">工资收入</div>
                            <div class="transaction-time">昨天 15:30</div>
                        </div>
                        <div class="transaction-amount amount-income">+¥8,250.00</div>
                    </div>
                </div>
            </div>
            <div class="screen-footer">
                <div class="nav-item active">
                    <i class="fas fa-home nav-icon"></i>
                    <span>首页</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-file-invoice-dollar nav-icon"></i>
                    <span>流水</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-chart-pie nav-icon"></i>
                    <span>报表</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-user nav-icon"></i>
                    <span>我的</span>
                </div>
            </div>
            <div class="screen-description">
                <h3>首页仪表盘</h3>
                <p>显示账户余额、收支统计，提供快捷记账入口和最近交易记录，底部导航栏可切换主要功能模块。</p>
            </div>
        </div>

        <!-- 记账页面 -->
        <div class="app-screen">
            <div class="screen-header">
                <i class="fas fa-arrow-left"></i>
                <div class="screen-title">记一笔</div>
                <i class="fas fa-check"></i>
            </div>
            <div class="screen-content">
                <div class="record-form">
                    <div class="amount-input-container">
                        <span class="amount-label">金额</span>
                        <input type="number" class="amount-input" placeholder="0.00">
                    </div>
                    <div class="form-group">
                        <div class="form-label">选择类型</div>
                        <div style="display: flex;">
                            <button class="form-input" style="width: 50%; border-right: none;">支出</button>
                            <button class="form-input" style="width: 50%; background-color: #f5f5f5;">收入</button>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="form-label">选择分类</div>
                        <div class="category-grid">
                            <div class="category-item active">
                                <div class="category-icon"><i class="fas fa-utensils"></i></div>
                                <div class="category-name">餐饮</div>
                            </div>
                            <div class="category-item">
                                <div class="category-icon"><i class="fas fa-home"></i></div>
                                <div class="category-name">住房</div>
                            </div>
                            <div class="category-item">
                                <div class="category-icon"><i class="fas fa-bus"></i></div>
                                <div class="category-name">交通</div>
                            </div>
                            <div class="category-item">
                                <div class="category-icon"><i class="fas fa-shopping-bag"></i></div>
                                <div class="category-name">购物</div>
                            </div>
                            <div class="category-item">
                                <div class="category-icon"><i class="fas fa-graduation-cap"></i></div>
                                <div class="category-name">教育</div>
                            </div>
                            <div class="category-item">
                                <div class="category-icon"><i class="fas fa-heart"></i></div>
                                <div class="category-name">医疗</div>
                            </div>
                            <div class="category-item">
                                <div class="category-icon"><i class="fas fa-film"></i></div>
                                <div class="category-name">娱乐</div>
                            </div>
                            <div class="category-item">
                                <div class="category-icon"><i class="fas fa-ellipsis-h"></i></div>
                                <div class="category-name">更多</div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">备注</label>
                        <input type="text" class="form-input" placeholder="添加备注（选填）">
                    </div>
                    <div class="form-group">
                        <label class="form-label">日期</label>
                        <input type="date" class="form-input">
                    </div>
                    <button class="save-btn">保存</button>
                </div>
            </div>
            <div class="screen-description">
                <h3>记账页面</h3>
                <p>提供快速记账功能，支持选择收支类型、分类、金额、备注和日期，界面简洁直观，操作流程短。</p>
            </div>
        </div>

        <!-- 交易记录页面 -->
        <div class="app-screen">
            <div class="screen-header">
                <div class="screen-title">交易记录</div>
                <i class="fas fa-filter"></i>
            </div>
            <div class="screen-content">
                <div style="padding: 15px; background-color: #f5f5f5; border-radius: 10px; margin-bottom: 20px;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                        <button class="form-input" style="width: 32%;">全部</button>
                        <button class="form-input" style="width: 32%; background-color: #f5f5f5;">支出</button>
                        <button class="form-input" style="width: 32%; background-color: #f5f5f5;">收入</button>
                    </div>
                    <div style="display: flex; justify-content: space-between;">
                        <button class="form-input" style="width: 48%;">本月</button>
                        <button class="form-input" style="width: 48%; background-color: #f5f5f5;">自定义</button>
                    </div>
                </div>
                <div class="recent-transactions">
                    <div class="transaction-item">
                        <div class="transaction-icon icon-food"><i class="fas fa-utensils"></i></div>
                        <div class="transaction-details">
                            <div class="transaction-title">午餐</div>
                            <div class="transaction-time">今天 12:30</div>
                        </div>
                        <div class="transaction-amount amount-expense">-¥45.00</div>
                    </div>
                    <div class="transaction-item">
                        <div class="transaction-icon icon-transport"><i class="fas fa-bus"></i></div>
                        <div class="transaction-details">
                            <div class="transaction-title">公交地铁</div>
                            <div class="transaction-time">今天 08:45</div>
                        </div>
                        <div class="transaction-amount amount-expense">-¥8.00</div>
                    </div>
                    <div class="transaction-item">
                        <div class="transaction-icon icon-shopping"><i class="fas fa-shopping-bag"></i></div>
                        <div class="transaction-details">
                            <div class="transaction-title">超市购物</div>
                            <div class="transaction-time">昨天 19:20</div>
                        </div>
                        <div class="transaction-amount amount-expense">-¥156.50</div>
                    </div>
                    <div class="transaction-item">
                        <div class="transaction-icon" style="background-color: #E8F5E9; color: #4CAF50;"><i class="fas fa-money-bill-wave"></i></div>
                        <div class="transaction-details">
                            <div class="transaction-title">工资收入</div>
                            <div class="transaction-time">昨天 15:30</div>
                        </div>
                        <div class="transaction-amount amount-income">+¥8,250.00</div>
                    </div>
                    <div class="transaction-item">
                        <div class="transaction-icon icon-food"><i class="fas fa-utensils"></i></div>
                        <div class="transaction-details">
                            <div class="transaction-title">晚餐</div>
                            <div class="transaction-time">前天 18:15</div>
                        </div>
                        <div class="transaction-amount amount-expense">-¥88.00</div>
                    </div>
                </div>
            </div>
            <div class="screen-footer">
                <div class="nav-item">
                    <i class="fas fa-home nav-icon"></i>
                    <span>首页</span>
                </div>
                <div class="nav-item active">
                    <i class="fas fa-file-invoice-dollar nav-icon"></i>
                    <span>流水</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-chart-pie nav-icon"></i>
                    <span>报表</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-user nav-icon"></i>
                    <span>我的</span>
                </div>
            </div>
            <div class="screen-description">
                <h3>交易记录页面</h3>
                <p>展示所有收支记录，支持按类型（全部/支出/收入）和时间筛选，每条记录显示分类、描述、时间和金额。</p>
            </div>
        </div>

        <!-- 报表分析页面 -->
        <div class="app-screen">
            <div class="screen-header">
                <div class="screen-title">报表分析</div>
                <i class="fas fa-ellipsis-v"></i>
            </div>
            <div class="screen-content">
                <div style="padding: 15px; background-color: #f5f5f5; border-radius: 10px; margin-bottom: 20px;">
                    <div style="display: flex; justify-content: space-between;">
                        <button class="form-input" style="width: 23%;">周</button>
                        <button class="form-input" style="width: 23%; background-color: white;">月</button>
                        <button class="form-input" style="width: 23%; background-color: #f5f5f5;">季</button>
                        <button class="form-input" style="width: 23%; background-color: #f5f5f5;">年</button>
                    </div>
                </div>
                <div class="chart-container">
                    <i class="fas fa-chart-line" style="font-size: 40px;"></i>
                </div>
                <div class="balance-stats" style="margin-bottom: 20px;">
                    <div class="stat-item">
                        <div class="stat-value">¥8,250.00</div>
                        <div class="stat-label">收入</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">¥5,320.20</div>
                        <div class="stat-label">支出</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">¥2,929.80</div>
                        <div class="stat-label">结余</div>
                    </div>
                </div>
                <div class="category-stats">
                    <div class="section-title">
                        <span>支出分类占比</span>
                    </div>
                    <div class="stat-bar-container">
                        <div class="stat-bar-header">
                            <span class="stat-bar-label">餐饮</span>
                            <span class="stat-bar-value">¥1,560.00 (29%)</span>
                        </div>
                        <div class="stat-bar">
                            <div class="stat-bar-fill" style="width: 29%;"></div>
                        </div>
                    </div>
                    <div class="stat-bar-container">
                        <div class="stat-bar-header">
                            <span class="stat-bar-label">住房</span>
                            <span class="stat-bar-value">¥2,000.00 (38%)</span>
                        </div>
                        <div class="stat-bar">
                            <div class="stat-bar-fill" style="width: 38%;"></div>
                        </div>
                    </div>
                    <div class="stat-bar-container">
                        <div class="stat-bar-header">
                            <span class="stat-bar-label">交通</span>
                            <span class="stat-bar-value">¥450.00 (8%)</span>
                        </div>
                        <div class="stat-bar">
                            <div class="stat-bar-fill" style="width: 8%;"></div>
                        </div>
                    </div>
                    <div class="stat-bar-container">
                        <div class="stat-bar-header">
                            <span class="stat-bar-label">购物</span>
                            <span class="stat-bar-value">¥850.00 (16%)</span>
                        </div>
                        <div class="stat-bar">
                            <div class="stat-bar-fill" style="width: 16%;"></div>
                        </div>
                    </div>
                    <div class="stat-bar-container">
                        <div class="stat-bar-header">
                            <span class="stat-bar-label">其他</span>
                            <span class="stat-bar-value">¥460.20 (9%)</span>
                        </div>
                        <div class="stat-bar">
                            <div class="stat-bar-fill" style="width: 9%;"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="screen-footer">
                <div class="nav-item">
                    <i class="fas fa-home nav-icon"></i>
                    <span>首页</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-file-invoice-dollar nav-icon"></i>
                    <span>流水</span>
                </div>
                <div class="nav-item active">
                    <i class="fas fa-chart-pie nav-icon"></i>
                    <span>报表</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-user nav-icon"></i>
                    <span>我的</span>
                </div>
            </div>
            <div class="screen-description">
                <h3>报表分析页面</h3>
                <p>展示收支趋势图表和分类占比统计，支持按周/月/季/年切换查看，帮助用户了解消费结构和财务状况。</p>
            </div>
        </div>

        <!-- AI智能分析页面 -->
        <div class="app-screen">
            <div class="screen-header">
                <div class="screen-title">AI智能分析</div>
            </div>
            <div class="screen-content">
                <div class="ai-card">
                    <h3 style="margin-bottom: 15px; color: #333;">本月消费分析</h3>
                    <p style="font-size: 14px; color: #666; margin-bottom: 15px;">您好！根据您本月的消费数据，AI助手为您提供以下分析和建议：</p>
                    <div class="ai-suggestion">
                        <div class="ai-suggestion-title">餐饮消费偏高</div>
                        <div class="ai-suggestion-content">您本月餐饮支出占比29%，高于上月15%。建议尝试在家做饭，可节省约30%的餐饮开支。</div>
                    </div>
                    <div class="ai-suggestion">
                        <div class="ai-suggestion-title">购物消费预警</div>
                        <div class="ai-suggestion-content">您本周购物类支出已达预算的80%，建议控制非必要消费。</div>
                    </div>
                    <div class="ai-suggestion">
                        <div class="ai-suggestion-title">收入来源分析</div>
                        <div class="ai-suggestion-content">工资收入占比95%，建议考虑增加副业收入来源，提高财务抗风险能力。</div>
                    </div>
                </div>
                <div class="section-title">
                    <span>智能记账助手</span>
                </div>
                <div style="background-color: #f5f5f5; border-radius: 15px; padding: 20px; text-align: center; margin-top: 15px;">
                    <i class="fas fa-microphone" style="font-size: 36px; color: #999; margin-bottom: 15px;"></i>
                    <p style="color: #666; font-size: 14px;">按住说话，AI帮您记账</p>
                </div>
            </div>
            <div class="screen-footer">
                <div class="nav-item">
                    <i class="fas fa-home nav-icon"></i>
                    <span>首页</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-file-invoice-dollar nav-icon"></i>
                    <span>流水</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-chart-pie nav-icon"></i>
                    <span>报表</span>
                </div>
                <div class="nav-item active">
                    <i class="fas fa-user nav-icon"></i>
                    <span>我的</span>
                </div>
            </div>
            <div class="screen-description">
                <h3>AI智能分析页面</h3>
                <p>基于用户消费数据提供智能分析和个性化建议，支持语音记账功能，自动识别消费场景和金额。</p>
            </div>
        </div>

        <!-- 设置页面 -->
        <div class="app-screen">
            <div class="screen-header">
                <div class="screen-title">设置</div>
            </div>
            <div class="screen-content">
                <div style="padding: 15px;">
                    <div style="display: flex; align-items: center; margin-bottom: 25px;">
                        <div style="width: 60px; height: 60px; border-radius: 30px; background-color: #E3F2FD; display: flex; align-items: center; justify-content: center; margin-right: 15px;">
                            <i class="fas fa-user" style="font-size: 24px; color: #4A90E2;"></i>
                        </div>
                        <div>
                            <div style="font-size: 18px; font-weight: 500; color: #333;">张三</div>
                            <div style="font-size: 14px; color: #666;">点击编辑个人资料</div>
                        </div>
                    </div>
                    <div style="border-top: 1px solid #eee; padding-top: 10px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 15px 0; border-bottom: 1px solid #eee;">
                            <div style="display: flex; align-items: center;">
                                <i class="fas fa-bank" style="font-size: 20px; color: #4A90E2; margin-right: 15px;"></i>
                                <span>账户管理</span>
                            </div>
                            <i class="fas fa-chevron-right" style="font-size: 16px; color: #999;"></i>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 15px 0; border-bottom: 1px solid #eee;">
                            <div style="display: flex; align-items: center;">
                                <i class="fas fa-shield-alt" style="font-size: 20px; color: #4A90E2; margin-right: 15px;"></i>
                                <span>安全设置</span>
                            </div>
                            <i class="fas fa-chevron-right" style="font-size: 16px; color: #999;"></i>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 15px 0; border-bottom: 1px solid #eee;">
                            <div style="display: flex; align-items: center;">
                                <i class="fas fa-bell" style="font-size: 20px; color: #4A90E2; margin-right: 15px;"></i>
                                <span>通知设置</span>
                            </div>
                            <i class="fas fa-chevron-right" style="font-size: 16px; color: #999;"></i>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 15px 0; border-bottom: 1px solid #eee;">
                            <div style="display: flex; align-items: center;">
                                <i class="fas fa-cog" style="font-size: 20px; color: #4A90E2; margin-right: 15px;"></i>
                                <span>通用设置</span>
                            </div>
                            <i class="fas fa-chevron-right" style="font-size: 16px; color: #999;"></i>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 15px 0; border-bottom: 1px solid #eee;">
                            <div style="display: flex; align-items: center;">
                                <i class="fas fa-question-circle" style="font-size: 20px; color: #4A90E2; margin-right: 15px;"></i>
                                <span>帮助与反馈</span>
                            </div>
                            <i class="fas fa-chevron-right" style="font-size: 16px; color: #999;"></i>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 15px 0; margin-top: 20px;">
                            <div style="display: flex; align-items: center; color: #F44336;">
                                <i class="fas fa-sign-out-alt" style="font-size: 20px; margin-right: 15px;"></i>
                                <span>退出登录</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="screen-footer">
                <div class="nav-item">
                    <i class="fas fa-home nav-icon"></i>
                    <span>首页</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-file-invoice-dollar nav-icon"></i>
                    <span>流水</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-chart-pie nav-icon"></i>
                    <span>报表</span>
                </div>
                <div class="nav-item active">
                    <i class="fas fa-user nav-icon"></i>
                    <span>我的</span>
                </div>
            </div>
            <div class="screen-description">
                <h3>设置页面</h3>
                <p>提供个人资料管理、账户安全、通知设置、通用设置等功能入口，支持用户自定义APP体验和退出登录。</p>
            </div>
        </div>
    </div>
</body>
</html>